#!/usr/bin/env python3
"""
检查TradingAgents-CN项目的日志系统和证券代码混淆问题
"""

import os
import json
import glob
from pathlib import Path
from datetime import datetime
import pandas as pd

def check_project_logging():
    """检查项目的日志系统配置和文件"""
    print("🔍 检查TradingAgents-CN项目日志系统")
    print("=" * 60)
    
    # 1. 检查日志配置
    print("\n📋 1. 日志配置检查")
    print("-" * 30)
    
    # 检查环境变量中的日志配置
    log_level = os.getenv("TRADINGAGENTS_LOG_LEVEL", "INFO")
    results_dir = os.getenv("TRADINGAGENTS_RESULTS_DIR", "./results")
    print(f"环境变量日志级别: {log_level}")
    print(f"结果目录: {results_dir}")
    
    # 检查配置文件
    config_dir = Path("config")
    if config_dir.exists():
        print(f"✅ 配置目录存在: {config_dir}")
        
        # 检查设置文件
        settings_file = config_dir / "settings.json"
        if settings_file.exists():
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                print(f"✅ 设置文件存在，日志级别: {settings.get('log_level', '未设置')}")
            except Exception as e:
                print(f"❌ 读取设置文件失败: {e}")
        
        # 检查使用记录文件
        usage_file = config_dir / "usage.json"
        if usage_file.exists():
            try:
                with open(usage_file, 'r', encoding='utf-8') as f:
                    usage_records = json.load(f)
                print(f"✅ 使用记录文件存在，记录数量: {len(usage_records)}")
                
                # 分析最近的使用记录
                if usage_records:
                    latest_record = usage_records[-1]
                    print(f"最新记录时间: {latest_record.get('timestamp')}")
                    print(f"分析类型: {latest_record.get('analysis_type')}")
                    print(f"会话ID: {latest_record.get('session_id')}")
            except Exception as e:
                print(f"❌ 读取使用记录失败: {e}")
    else:
        print(f"❌ 配置目录不存在: {config_dir}")
    
    # 2. 检查缓存系统
    print("\n📦 2. 缓存系统检查")
    print("-" * 30)
    
    cache_dir = Path("tradingagents/dataflows/data_cache")
    if cache_dir.exists():
        print(f"✅ 缓存目录存在: {cache_dir}")
        
        # 检查股票数据缓存
        stock_cache_dir = cache_dir / "stock_data"
        metadata_dir = cache_dir / "metadata"
        
        if stock_cache_dir.exists():
            stock_files = list(stock_cache_dir.glob("*.txt"))
            print(f"股票数据缓存文件数量: {len(stock_files)}")
            
            # 检查000001和000791的缓存
            code_001_files = [f for f in stock_files if "000001" in f.name]
            code_791_files = [f for f in stock_files if "000791" in f.name]
            
            print(f"000001(平安银行)缓存文件: {len(code_001_files)}")
            for f in code_001_files:
                print(f"  - {f.name}")
            
            print(f"000791缓存文件: {len(code_791_files)}")
            for f in code_791_files:
                print(f"  - {f.name}")
        
        if metadata_dir.exists():
            metadata_files = list(metadata_dir.glob("*.json"))
            print(f"元数据文件数量: {len(metadata_files)}")
            
            # 分析000001和000791的元数据
            for meta_file in metadata_files:
                if "000001" in meta_file.name or "000791" in meta_file.name:
                    try:
                        with open(meta_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        print(f"📄 {meta_file.name}:")
                        print(f"  股票代码: {metadata.get('symbol')}")
                        print(f"  缓存时间: {metadata.get('cached_at')}")
                        print(f"  数据源: {metadata.get('data_source')}")
                    except Exception as e:
                        print(f"❌ 读取元数据失败 {meta_file}: {e}")
    
    # 3. 检查结果目录
    print("\n📁 3. 结果目录检查")
    print("-" * 30)
    
    results_path = Path(results_dir)
    if results_path.exists():
        print(f"✅ 结果目录存在: {results_path}")
        
        # 查找所有子目录（按股票代码）
        stock_dirs = [d for d in results_path.iterdir() if d.is_dir()]
        print(f"股票分析目录数量: {len(stock_dirs)}")
        
        for stock_dir in stock_dirs:
            print(f"📂 {stock_dir.name}")
            
            # 查找日志文件
            log_files = list(stock_dir.rglob("*.log"))
            if log_files:
                print(f"  日志文件: {len(log_files)}")
                for log_file in log_files:
                    print(f"    - {log_file.relative_to(stock_dir)}")
    else:
        print(f"❌ 结果目录不存在: {results_path}")
    
    # 4. 检查可能的证券代码混淆
    print("\n🔍 4. 证券代码混淆检查")
    print("-" * 30)
    
    # 检查缓存文件内容
    if cache_dir.exists():
        stock_cache_dir = cache_dir / "stock_data"
        
        # 检查000791文件是否包含000001的内容
        code_791_files = list(stock_cache_dir.glob("*000791*.txt"))
        
        for file_path in code_791_files:
            print(f"\n📄 检查文件: {file_path.name}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含000001相关内容
                if "000001" in content:
                    print("⚠️  发现000791文件中包含000001内容!")
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if "000001" in line:
                            print(f"  第{i}行: {line.strip()}")
                else:
                    print("✅ 未发现000001内容混淆")
                
                # 检查股票名称
                if "平安银行" in content:
                    print("⚠️  发现000791文件中包含平安银行信息!")
                
            except Exception as e:
                print(f"❌ 读取文件失败: {e}")
    
    # 5. 生成日志系统报告
    print("\n📊 5. 日志系统总结")
    print("-" * 30)
    
    print("✅ 项目具有以下日志记录机制:")
    print("  1. 配置管理器中的使用记录跟踪")
    print("  2. 数据缓存系统的元数据记录")
    print("  3. CLI工具的消息日志记录")
    print("  4. 调试模式下的详细输出")
    
    print("\n💡 建议改进:")
    print("  1. 添加统一的logging模块配置")
    print("  2. 实现结构化日志记录")
    print("  3. 添加股票代码验证机制")
    print("  4. 实现分析过程的完整审计日志")

def check_stock_code_confusion():
    """专门检查000001和000791的混淆问题"""
    print("\n🎯 专项检查: 000001与000791证券代码混淆")
    print("=" * 60)
    
    cache_dir = Path("tradingagents/dataflows/data_cache")
    
    if not cache_dir.exists():
        print("❌ 缓存目录不存在")
        return
    
    stock_cache_dir = cache_dir / "stock_data"
    metadata_dir = cache_dir / "metadata"
    
    # 获取所有相关文件
    all_files = list(stock_cache_dir.glob("*.txt"))
    
    print(f"总缓存文件数: {len(all_files)}")
    
    # 分析每个文件的内容
    confusion_found = False
    
    for file_path in all_files:
        file_name = file_path.name
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取文件中声明的股票代码
            declared_code = None
            if "000001" in file_name:
                declared_code = "000001"
            elif "000791" in file_name:
                declared_code = "000791"
            else:
                continue
            
            # 检查内容中的股票代码
            content_codes = set()
            lines = content.split('\n')
            
            for line in lines:
                if "000001" in line:
                    content_codes.add("000001")
                if "000791" in line:
                    content_codes.add("000791")
            
            # 检查是否有混淆
            if declared_code and content_codes:
                if declared_code not in content_codes:
                    print(f"⚠️  文件 {file_name} 声明代码 {declared_code} 但内容中未找到")
                    confusion_found = True
                
                unexpected_codes = content_codes - {declared_code}
                if unexpected_codes:
                    print(f"⚠️  文件 {file_name} 包含意外的股票代码: {unexpected_codes}")
                    confusion_found = True
                    
                    # 显示具体的混淆行
                    for i, line in enumerate(lines, 1):
                        for code in unexpected_codes:
                            if code in line:
                                print(f"    第{i}行: {line.strip()}")
        
        except Exception as e:
            print(f"❌ 检查文件 {file_name} 失败: {e}")
    
    if not confusion_found:
        print("✅ 未发现000001和000791之间的代码混淆问题")
    
    return confusion_found

if __name__ == "__main__":
    # 检查项目日志系统
    check_project_logging()
    
    # 检查证券代码混淆
    confusion_detected = check_stock_code_confusion()
    
    print(f"\n{'='*60}")
    print("🏁 检查完成")
    
    if confusion_detected:
        print("⚠️  发现证券代码混淆问题，建议进一步调查")
    else:
        print("✅ 未发现明显的证券代码混淆问题")
