#!/usr/bin/env python3
"""
测试启动脚本修复效果
"""

import sys
import os
from pathlib import Path

def test_pythonpath_setup():
    """测试PYTHONPATH设置"""
    
    print("🧪 测试PYTHONPATH设置")
    print("=" * 40)
    
    # 检查当前PYTHONPATH
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    print(f"当前PYTHONPATH: {current_pythonpath}")
    
    # 检查项目根目录是否在Python路径中
    project_root = "/mnt/e/AI/TradingAgents-CN"
    
    if project_root in sys.path:
        print(f"✅ 项目根目录已在sys.path中: {project_root}")
    else:
        print(f"❌ 项目根目录不在sys.path中: {project_root}")
        print("💡 需要设置PYTHONPATH或使用python -m方式运行")
    
    # 测试模块导入
    print("\n📦 测试模块导入")
    print("-" * 20)
    
    try:
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        print("✅ tradingagents.graph.trading_graph 导入成功")
    except ImportError as e:
        print(f"❌ tradingagents.graph.trading_graph 导入失败: {e}")
    
    try:
        from tradingagents.default_config import DEFAULT_CONFIG
        print("✅ tradingagents.default_config 导入成功")
    except ImportError as e:
        print(f"❌ tradingagents.default_config 导入失败: {e}")
    
    try:
        import cli.main
        print("✅ cli.main 模块导入成功")
    except ImportError as e:
        print(f"❌ cli.main 模块导入失败: {e}")

def test_startup_script_changes():
    """测试启动脚本的修改"""
    
    print("\n🔧 测试启动脚本修改")
    print("=" * 40)
    
    script_path = Path("quick_start.sh")
    
    if not script_path.exists():
        print("❌ quick_start.sh 文件不存在")
        return
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查PYTHONPATH设置
    if "export PYTHONPATH=" in content:
        print("✅ 脚本包含PYTHONPATH设置")
    else:
        print("❌ 脚本缺少PYTHONPATH设置")
    
    # 检查CLI启动方式
    if "python -m cli.main" in content:
        print("✅ CLI使用模块方式启动")
    else:
        print("❌ CLI未使用模块方式启动")
    
    # 检查是否移除了000791选项
    if "分析000791" not in content:
        print("✅ 已移除000791分析选项")
    else:
        print("❌ 仍包含000791分析选项")
    
    # 检查选项数量
    if "请选择 (1-3)" in content:
        print("✅ 选项数量已更新为1-3")
    else:
        print("❌ 选项数量未正确更新")

def generate_usage_guide():
    """生成使用指南"""
    
    print("\n📖 修复后的使用指南")
    print("=" * 40)
    
    guide = """
## 🚀 TradingAgents-CN 启动脚本修复说明

### ✅ 修复内容
1. **PYTHONPATH设置**: 自动设置项目根目录到Python路径
2. **CLI启动方式**: 使用 `python -m cli.main` 替代直接调用
3. **选项简化**: 移除000791分析选项，保留核心功能
4. **错误处理**: 添加更好的错误提示和调试信息

### 📋 使用方法

#### 方法1: 使用修复后的启动脚本 (推荐)
```bash
./quick_start.sh
```

#### 方法2: 手动设置环境
```bash
# 设置PYTHONPATH
export PYTHONPATH=/mnt/e/AI/TradingAgents-CN:$PYTHONPATH

# 启动CLI
python -m cli.main

# 或启动Web界面
python -m streamlit run web/app.py
```

#### 方法3: 在项目根目录直接运行
```bash
cd /mnt/e/AI/TradingAgents-CN
python -m cli.main
```

### 🎯 启动选项说明

1. **Web界面 (推荐)**
   - 启动Streamlit Web应用
   - 浏览器访问: http://localhost:8501
   - 图形化界面，易于使用

2. **CLI命令行**
   - 交互式命令行界面
   - 适合高级用户和脚本化操作
   - 支持批量分析

3. **测试数据源**
   - 检查各种数据源连接状态
   - 验证API配置
   - 测试通达信、Yahoo Finance等

### ⚠️ 故障排除

如果仍然遇到模块导入错误：

1. **检查conda环境**:
   ```bash
   conda activate TACN
   which python
   ```

2. **手动设置PYTHONPATH**:
   ```bash
   export PYTHONPATH=/mnt/e/AI/TradingAgents-CN:$PYTHONPATH
   echo $PYTHONPATH
   ```

3. **验证模块导入**:
   ```bash
   python -c "from tradingagents.default_config import DEFAULT_CONFIG; print('导入成功')"
   ```

4. **使用绝对路径**:
   ```bash
   cd /mnt/e/AI/TradingAgents-CN
   /home/<USER>/miniconda/envs/TACN/bin/python -m cli.main
   ```

### 💡 最佳实践

1. **始终在项目根目录运行脚本**
2. **确保conda环境已激活**
3. **使用模块方式运行Python脚本**
4. **检查PYTHONPATH设置**
"""
    
    print(guide)
    
    # 保存指南到文件
    with open("startup_fix_guide.md", 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("📝 使用指南已保存到: startup_fix_guide.md")

if __name__ == "__main__":
    print("🔧 TradingAgents-CN 启动脚本修复测试")
    print("=" * 50)
    
    test_pythonpath_setup()
    test_startup_script_changes()
    generate_usage_guide()
    
    print(f"\n{'='*50}")
    print("🏁 测试完成")
    print()
    print("💡 修复总结:")
    print("- ✅ 添加了PYTHONPATH自动设置")
    print("- ✅ 修复了CLI模块导入问题")
    print("- ✅ 移除了000791分析选项")
    print("- ✅ 简化了启动选项为1-3")
    print()
    print("🚀 现在可以正常使用 ./quick_start.sh 启动项目了！")
