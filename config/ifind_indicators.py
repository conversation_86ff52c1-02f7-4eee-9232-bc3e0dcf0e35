#!/usr/bin/env python3
"""
iFinD指标配置文件
基于官方indicators参数说明文档
"""

# 基础行情指标
BASIC_INDICATORS = {
    'preClose': '前收盘价',
    'open': '开盘价',
    'high': '最高价',
    'low': '最低价',
    'close': '收盘价',
    'avgPrice': '均价',
    'change': '涨跌',
    'changeRatio': '涨跌幅',
    'volume': '成交量',
    'amount': '成交额',
    'turnoverRatio': '换手率',
    'transactionAmount': '成交笔数'
}

# 估值指标
VALUATION_INDICATORS = {
    'totalShares': '总股本',
    'totalCapital': '总市值',
    'floatSharesOfAShares': 'A股流通股本',
    'floatSharesOfBShares': 'B股流通股本',
    'floatCapitalOfAShares': 'A股流通市值',
    'floatCapitalOfBShares': 'B股流通市值',
    'pe_ttm': '市盈率（TTM）',
    'pe': 'PE市盈率',
    'pb': 'PB市净率',
    'ps': 'PS市销率',
    'pcf': 'PCF市现率'
}

# 交易状态指标
TRADING_STATUS_INDICATORS = {
    'ths_trading_status_stock': '交易状态',
    'ths_up_and_down_status_stock': '涨跌停状态',
    'ths_af_stock': '复权因子',
    'ths_vol_after_trading_stock': '盘后成交量',
    'ths_trans_num_after_trading_stock': '盘后成交笔数',
    'ths_amt_after_trading_stock': '盘后成交额',
    'ths_vaild_turnover_stock': '有效换手率'
}

# 基金专用指标
FUND_INDICATORS = {
    'netAssetValue': '单位净值',
    'adjustedNAV': '复权单位净值',
    'accumulatedNAV': '累计单位净值',
    'premium': '贴水',
    'premiumRatio': '贴水率',
    'estimatedPosition': '估算仓位'
}

# 指数专用指标
INDEX_INDICATORS = {
    'floatCapital': '流通市值',
    'pe_ttm_index': 'PE(TTM)',
    'pb_mrq': 'PB(MRQ)',
    'pe_indexPublisher': 'PE(指数发布方）'
}

# 债券专用指标
BOND_INDICATORS = {
    'yieldMaturity': '到期收益率',
    'remainingTerm': '剩余期限',
    'maxwellDuration': '麦氏久期',
    'modifiedDuration': '修正久期',
    'convexity': '凸性'
}

# 外汇专用指标
FOREX_INDICATORS = {
    'close_2330': '收盘价（23：30）'
}

# 期权专用指标
OPTION_INDICATORS = {
    'openInterest': '持仓量',
    'positionChange': '持仓变动'
}

# 期货专用指标
FUTURES_INDICATORS = {
    'preSettlement': '前结算价',
    'settlement': '结算价',
    'change_settlement': '涨跌（结算价）',
    'chg_settlement': '涨跌幅（结算价）',
    'openInterest': '持仓量',
    'positionChange': '持仓变动',
    'amplitude': '振幅'
}

# 复权方式配置
ADJUST_TYPES = {
    'none': {'code': 1, 'name': '不复权'},
    'forward_reinvest': {'code': 2, 'name': '前复权（分红再投）'},
    'backward_reinvest': {'code': 3, 'name': '后复权（分红再投）'},
    'forward_full_reinvest': {'code': 4, 'name': '全流通前复权（分红再投）'},
    'backward_full_reinvest': {'code': 5, 'name': '全流通后复权（分红再投）'},
    'forward_cash': {'code': 6, 'name': '前复权（现金分红）'},
    'backward_cash': {'code': 7, 'name': '后复权（现金分红）'}
}

# 时间周期配置
TIME_INTERVALS = {
    'daily': {'code': 'D', 'name': '日'},
    'weekly': {'code': 'W', 'name': '周'},
    'monthly': {'code': 'M', 'name': '月'},
    'quarterly': {'code': 'Q', 'name': '季'},
    'semi_annual': {'code': 'S', 'name': '半年'},
    'yearly': {'code': 'Y', 'name': '年'}
}

# 货币类型配置
CURRENCY_TYPES = {
    'usd': {'code': 'MHB', 'name': '美元'},
    'hkd': {'code': 'GHB', 'name': '港元'},
    'rmb': {'code': 'RMB', 'name': '人民币'},
    'original': {'code': 'YSHB', 'name': '原始货币'}
}

# 报价类型配置（债券专用）
PRICE_TYPES = {
    'full': {'code': 1, 'name': '全价'},
    'clean': {'code': 2, 'name': '净价'}
}

# 缺失值处理配置
FILL_METHODS = {
    'previous': 'Previous',  # 沿用之前数据
    'blank': 'Blank',        # 空值
    'omit': 'Omit'          # 缺省值
}

# 预定义指标组合
INDICATOR_GROUPS = {
    'basic': {
        'name': '基础行情',
        'indicators': ['preClose', 'open', 'high', 'low', 'close', 'volume', 'amount']
    },
    'valuation': {
        'name': '估值分析',
        'indicators': ['pe_ttm', 'pb', 'ps', 'pcf', 'totalCapital', 'floatCapitalOfAShares']
    },
    'trading': {
        'name': '交易分析',
        'indicators': ['turnoverRatio', 'transactionAmount', 'ths_vaild_turnover_stock']
    },
    'comprehensive': {
        'name': '综合分析',
        'indicators': ['preClose', 'open', 'high', 'low', 'close', 'volume', 'amount', 
                      'turnoverRatio', 'pe_ttm', 'pb', 'totalCapital']
    },
    'fund_analysis': {
        'name': '基金分析',
        'indicators': ['netAssetValue', 'adjustedNAV', 'accumulatedNAV', 'premium', 
                      'premiumRatio', 'estimatedPosition']
    },
    'bond_analysis': {
        'name': '债券分析',
        'indicators': ['yieldMaturity', 'remainingTerm', 'maxwellDuration', 
                      'modifiedDuration', 'convexity']
    }
}

# 市场类型与指标映射
MARKET_INDICATORS = {
    'stock': {
        'name': '股票',
        'available_groups': ['basic', 'valuation', 'trading', 'comprehensive'],
        'special_indicators': TRADING_STATUS_INDICATORS
    },
    'fund': {
        'name': '基金',
        'available_groups': ['fund_analysis'],
        'special_indicators': FUND_INDICATORS
    },
    'bond': {
        'name': '债券',
        'available_groups': ['bond_analysis'],
        'special_indicators': BOND_INDICATORS
    },
    'index': {
        'name': '指数',
        'available_groups': ['basic', 'valuation'],
        'special_indicators': INDEX_INDICATORS
    },
    'futures': {
        'name': '期货',
        'available_groups': ['basic'],
        'special_indicators': FUTURES_INDICATORS
    },
    'option': {
        'name': '期权',
        'available_groups': ['basic'],
        'special_indicators': OPTION_INDICATORS
    }
}

def get_indicators_by_group(group_name: str) -> list:
    """根据指标组名获取指标列表"""
    if group_name in INDICATOR_GROUPS:
        return INDICATOR_GROUPS[group_name]['indicators']
    return []

def get_indicators_by_market(market_type: str) -> dict:
    """根据市场类型获取可用指标"""
    if market_type in MARKET_INDICATORS:
        market_info = MARKET_INDICATORS[market_type]
        available_indicators = {}
        
        # 添加基础指标组
        for group in market_info['available_groups']:
            if group in INDICATOR_GROUPS:
                group_indicators = INDICATOR_GROUPS[group]['indicators']
                for indicator in group_indicators:
                    if indicator in BASIC_INDICATORS:
                        available_indicators[indicator] = BASIC_INDICATORS[indicator]
                    elif indicator in VALUATION_INDICATORS:
                        available_indicators[indicator] = VALUATION_INDICATORS[indicator]
        
        # 添加特殊指标
        available_indicators.update(market_info['special_indicators'])
        
        return available_indicators
    
    return {}

def build_function_para(adjust_type: str = 'forward_cash', 
                       interval: str = 'daily',
                       currency: str = 'original',
                       fill_method: str = 'previous',
                       price_type: str = 'full') -> dict:
    """构建functionpara参数"""
    
    function_para = {}
    
    # 复权方式
    if adjust_type in ADJUST_TYPES:
        function_para['CPS'] = ADJUST_TYPES[adjust_type]['code']
    
    # 时间周期
    if interval in TIME_INTERVALS:
        function_para['Interval'] = TIME_INTERVALS[interval]['code']
    
    # 货币类型
    if currency in CURRENCY_TYPES:
        function_para['Currency'] = CURRENCY_TYPES[currency]['code']
    
    # 缺失值处理
    if fill_method in FILL_METHODS:
        function_para['Fill'] = FILL_METHODS[fill_method]
    
    # 报价类型（债券专用）
    if price_type in PRICE_TYPES:
        function_para['PriceType'] = str(PRICE_TYPES[price_type]['code'])
    
    return function_para

# 使用示例
if __name__ == "__main__":
    print("📊 iFinD指标配置示例")
    print("=" * 40)
    
    # 获取股票基础指标
    stock_indicators = get_indicators_by_market('stock')
    print(f"股票可用指标数量: {len(stock_indicators)}")
    
    # 获取基础行情指标组
    basic_indicators = get_indicators_by_group('basic')
    print(f"基础行情指标: {basic_indicators}")
    
    # 构建功能参数
    func_para = build_function_para(
        adjust_type='forward_cash',
        interval='daily',
        currency='original'
    )
    print(f"功能参数: {func_para}")
