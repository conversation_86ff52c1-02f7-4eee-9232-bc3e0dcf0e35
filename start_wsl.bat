@echo off
REM TradingAgents-CN WSL启动器 (Windows批处理版本)
REM 此脚本从Windows调用WSL中的启动脚本

echo 🚀 TradingAgents-CN WSL启动器
echo ================================

REM 检查WSL是否可用
wsl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ WSL未安装或不可用
    echo 💡 请先安装WSL: https://docs.microsoft.com/zh-cn/windows/wsl/install
    pause
    exit /b 1
)

echo ✅ WSL环境检查通过

REM 显示选项
echo.
echo 🎯 启动选项:
echo 1^) 完整启动脚本 ^(推荐^)
echo 2^) 快速启动脚本
echo 3^) 直接启动Web界面
echo 4^) 直接启动CLI
echo.

set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo 🔄 启动完整WSL脚本...
    wsl bash -c "cd /mnt/e/AI/TradingAgents-CN && ./start_wsl.sh"
) else if "%choice%"=="2" (
    echo 🔄 启动快速WSL脚本...
    wsl bash -c "cd /mnt/e/AI/TradingAgents-CN && ./quick_start_wsl.sh"
) else if "%choice%"=="3" (
    echo 🌐 直接启动Web界面...
    echo 💡 浏览器访问: http://localhost:8501
    wsl bash -c "cd /mnt/e/AI/TradingAgents-CN && source ~/miniconda3/etc/profile.d/conda.sh && conda activate TACN && python -m streamlit run web/app.py --server.port 8501 --server.address localhost"
) else if "%choice%"=="4" (
    echo 🖥️ 直接启动CLI...
    wsl bash -c "cd /mnt/e/AI/TradingAgents-CN && source ~/miniconda3/etc/profile.d/conda.sh && conda activate TACN && python cli/main.py"
) else (
    echo ⚠️ 无效选择，启动快速脚本
    wsl bash -c "cd /mnt/e/AI/TradingAgents-CN && ./quick_start_wsl.sh"
)

echo.
echo 🏁 脚本执行完成
pause
