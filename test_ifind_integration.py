#!/usr/bin/env python3
"""
测试iFinD HTTP API集成
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 加载环境变量
load_dotenv()

def test_ifind_basic_connection():
    """测试iFinD基础连接"""
    print("🧪 测试iFinD HTTP API基础连接")
    print("=" * 50)
    
    # 检查环境变量
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    
    if not refresh_token or refresh_token == 'your_ifind_refresh_token_here':
        print("❌ 未配置IFIND_REFRESH_TOKEN环境变量")
        print("💡 请在.env文件中配置您的iFinD refresh_token")
        print("   IFIND_REFRESH_TOKEN=your_actual_refresh_token")
        return False
    
    try:
        from tradingagents.dataflows.ifind_utils import IFindDataProvider
        
        # 创建数据提供器
        provider = IFindDataProvider(refresh_token)
        
        # 测试连接
        success = provider.test_connection()
        
        if success:
            print("✅ iFinD HTTP API连接成功")
            return True
        else:
            print("❌ iFinD HTTP API连接失败")
            return False
            
    except Exception as e:
        print(f"❌ iFinD连接测试失败: {e}")
        return False

def test_ifind_realtime_data():
    """测试iFinD实时数据获取"""
    print("\n🧪 测试iFinD实时数据获取")
    print("=" * 50)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token or refresh_token == 'your_ifind_refresh_token_here':
        print("❌ 未配置IFIND_REFRESH_TOKEN，跳过测试")
        return False
    
    try:
        from tradingagents.dataflows.ifind_utils import IFindDataProvider
        
        provider = IFindDataProvider(refresh_token)
        
        # 测试A股实时数据
        test_stocks = ['000001', '600036', '000858']
        
        for stock_code in test_stocks:
            print(f"\n📊 测试股票: {stock_code}")
            
            realtime_data = provider.get_realtime_data(stock_code)
            
            if realtime_data:
                print(f"✅ 获取成功")
                print(f"   最新价: {realtime_data.get('latest', 'N/A')}")
                print(f"   开盘价: {realtime_data.get('open', 'N/A')}")
                print(f"   最高价: {realtime_data.get('high', 'N/A')}")
                print(f"   最低价: {realtime_data.get('low', 'N/A')}")
                print(f"   成交量: {realtime_data.get('volume', 'N/A')}")
            else:
                print(f"❌ 获取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 实时数据测试失败: {e}")
        return False

def test_ifind_historical_data():
    """测试iFinD历史数据获取"""
    print("\n🧪 测试iFinD历史数据获取")
    print("=" * 50)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token or refresh_token == 'your_ifind_refresh_token_here':
        print("❌ 未配置IFIND_REFRESH_TOKEN，跳过测试")
        return False
    
    try:
        from tradingagents.dataflows.ifind_utils import IFindDataProvider
        
        provider = IFindDataProvider(refresh_token)
        
        # 测试历史数据
        stock_code = '000001'
        start_date = '2025-06-01'
        end_date = '2025-07-02'
        
        print(f"📈 测试股票: {stock_code}")
        print(f"📅 时间范围: {start_date} 到 {end_date}")
        
        df = provider.get_historical_data(stock_code, start_date, end_date)
        
        if not df.empty:
            print(f"✅ 获取成功")
            print(f"   数据条数: {len(df)}")
            print(f"   列名: {list(df.columns)}")
            print(f"   日期范围: {df['date'].min()} 到 {df['date'].max()}")
            print(f"   最高价: {df['high'].max():.2f}")
            print(f"   最低价: {df['low'].min():.2f}")
            
            # 显示最近几条数据
            print("\n最近5条数据:")
            print(df.tail().to_string())
        else:
            print(f"❌ 获取失败，返回空数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 历史数据测试失败: {e}")
        return False

def test_ifind_integration_function():
    """测试iFinD集成函数"""
    print("\n🧪 测试iFinD集成函数")
    print("=" * 50)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token or refresh_token == 'your_ifind_refresh_token_here':
        print("❌ 未配置IFIND_REFRESH_TOKEN，跳过测试")
        return False
    
    try:
        from tradingagents.dataflows.ifind_utils import get_ifind_stock_data
        
        # 测试集成函数
        stock_code = '000001'
        start_date = '2025-06-01'
        end_date = '2025-07-02'
        
        print(f"📊 测试集成函数: {stock_code}")
        
        result = get_ifind_stock_data(stock_code, start_date, end_date, refresh_token)
        
        if "❌" not in result:
            print("✅ 集成函数测试成功")
            print("\n📋 返回结果预览:")
            print(result[:500] + "..." if len(result) > 500 else result)
        else:
            print("❌ 集成函数测试失败")
            print(f"错误信息: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成函数测试失败: {e}")
        return False

def test_ifind_vs_existing_sources():
    """对比iFinD与现有数据源"""
    print("\n🧪 对比iFinD与现有数据源")
    print("=" * 50)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token or refresh_token == 'your_ifind_refresh_token_here':
        print("❌ 未配置IFIND_REFRESH_TOKEN，跳过对比测试")
        return False
    
    try:
        # 测试同一股票的不同数据源
        stock_code = '000001'
        start_date = '2025-06-01'
        end_date = '2025-07-02'
        
        print(f"📊 对比股票: {stock_code}")
        print(f"📅 时间范围: {start_date} 到 {end_date}")
        
        # iFinD数据
        print("\n1. iFinD数据源:")
        try:
            from tradingagents.dataflows.ifind_utils import get_ifind_stock_data
            ifind_result = get_ifind_stock_data(stock_code, start_date, end_date, refresh_token)
            print("✅ iFinD数据获取成功")
            print(f"   结果长度: {len(ifind_result)} 字符")
        except Exception as e:
            print(f"❌ iFinD数据获取失败: {e}")
        
        # 通达信数据
        print("\n2. 通达信数据源:")
        try:
            from tradingagents.dataflows.tdx_utils import get_china_stock_data
            tdx_result = get_china_stock_data(stock_code, start_date, end_date)
            print("✅ 通达信数据获取成功")
            print(f"   结果长度: {len(tdx_result)} 字符")
        except Exception as e:
            print(f"❌ 通达信数据获取失败: {e}")
        
        # Yahoo Finance数据
        print("\n3. Yahoo Finance数据源:")
        try:
            from tradingagents.dataflows.yfin_utils import get_YFin_data_online
            yahoo_result = get_YFin_data_online(f"{stock_code}.SS", start_date, end_date)
            print("✅ Yahoo Finance数据获取成功")
            print(f"   结果长度: {len(str(yahoo_result))} 字符")
        except Exception as e:
            print(f"❌ Yahoo Finance数据获取失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据源对比测试失败: {e}")
        return False

def generate_integration_guide():
    """生成集成指南"""
    print("\n📖 iFinD HTTP API集成指南")
    print("=" * 50)
    
    guide = """
## 🚀 iFinD HTTP API集成完成

### ✅ 已完成的功能
1. **基础连接**: iFinD HTTP API连接和认证
2. **实时数据**: 获取股票实时行情
3. **历史数据**: 获取股票历史K线数据
4. **数据格式化**: 统一的数据输出格式
5. **错误处理**: 完善的异常处理机制
6. **Token管理**: 自动管理access_token刷新

### 🔧 配置步骤

#### 1. 获取refresh_token
- 安装同花顺iFinD客户端
- 登录后在"工具-refresh_token更新"中获取
- 复制refresh_token到.env文件

#### 2. 配置环境变量
```bash
# 在.env文件中添加
IFIND_REFRESH_TOKEN=your_actual_refresh_token_here
```

#### 3. 测试连接
```bash
python test_ifind_integration.py
```

### 📊 使用方法

#### 在分析师工具中使用
```python
from tradingagents.dataflows.ifind_utils import get_ifind_stock_data

# 获取股票数据
data = get_ifind_stock_data("000001", "2025-06-01", "2025-07-02", refresh_token)
```

#### 在Web界面中使用
- 数据源选择中会自动包含iFinD选项
- 配置refresh_token后即可使用

### 🎯 优势特点
- **专业数据**: 同花顺专业金融数据
- **实时性强**: 快速的数据更新
- **覆盖全面**: A股、港股、美股数据
- **技术指标**: 丰富的技术分析指标
- **无SDK依赖**: 纯HTTP API，跨平台兼容

### ⚠️ 注意事项
1. **Token管理**: refresh_token需要定期更新
2. **访问限制**: 注意API调用频率限制
3. **网络稳定**: 确保网络连接稳定
4. **错误处理**: 监控API调用状态

### 🔄 与现有数据源的关系
- **主要数据源**: 可作为A股数据的主要来源
- **备用方案**: 与通达信、Yahoo Finance形成互补
- **优先级配置**: 支持数据源优先级设置
- **平滑切换**: 支持数据源间的无缝切换
"""
    
    print(guide)
    
    # 保存指南到文件
    with open("iFinD_integration_guide.md", 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("📝 集成指南已保存到: iFinD_integration_guide.md")

def main():
    """主测试函数"""
    print("🧪 iFinD HTTP API集成测试套件")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("基础连接测试", test_ifind_basic_connection),
        ("实时数据测试", test_ifind_realtime_data),
        ("历史数据测试", test_ifind_historical_data),
        ("集成函数测试", test_ifind_integration_function),
        ("数据源对比测试", test_ifind_vs_existing_sources),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 生成测试报告
    print(f"\n{'='*60}")
    print("🏁 测试结果汇总")
    print("-" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} | {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！iFinD HTTP API集成成功")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")
    
    # 生成集成指南
    generate_integration_guide()

if __name__ == "__main__":
    main()
