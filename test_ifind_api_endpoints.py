#!/usr/bin/env python3
"""
测试iFinD API的正确端点
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_different_endpoints():
    """测试不同的API端点"""
    print("🔍 测试iFinD API端点")
    print("=" * 50)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    if not access_token:
        print("❌ 未找到IFIND_ACCESS_TOKEN")
        return
    
    print(f"✅ 使用access_token: {access_token[:20]}...")
    
    # 基础URL
    base_urls = [
        "https://ft.10jqka.com.cn",
        "https://quantapi.51ifind.com"
    ]
    
    # 可能的API端点
    endpoints = [
        "/api/v1/real_time_quotation",
        "/ds_service/api/v1/real_time_quotation", 
        "/api/real_time_quotation",
        "/real_time_quotation",
        "/api/v1/quotation",
        "/quotation"
    ]
    
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    params = {
        "codes": "000001.SZ",
        "indicators": "open,high,low,latest"
    }
    
    print("\n📊 测试实时行情端点:")
    print("-" * 30)
    
    for base_url in base_urls:
        print(f"\n🌐 测试基础URL: {base_url}")
        
        for endpoint in endpoints:
            url = f"{base_url}{endpoint}"
            
            try:
                print(f"  尝试: {endpoint}")
                response = requests.post(url, json=params, headers=headers, timeout=10)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        api_status = result.get('status_code', 'N/A')
                        print(f"    API状态: {api_status}")
                        
                        if api_status == 0:
                            print(f"    ✅ 成功！正确端点: {url}")
                            print(f"    响应数据: {json.dumps(result, ensure_ascii=False)[:200]}...")
                            return url
                        else:
                            reason = result.get('reason', '未知')
                            print(f"    ⚠️ API错误: {reason}")
                    except json.JSONDecodeError:
                        print(f"    ❌ JSON解析失败")
                elif response.status_code == 404:
                    print(f"    ❌ 端点不存在")
                else:
                    print(f"    ❌ HTTP错误: {response.text[:100]}")
                    
            except Exception as e:
                print(f"    ❌ 请求失败: {e}")
    
    print("\n❌ 未找到有效的实时行情端点")
    return None

def test_basic_api_info():
    """测试基础API信息"""
    print("\n🔍 测试基础API信息")
    print("=" * 50)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    # 测试API信息端点
    info_endpoints = [
        "/api/v1/info",
        "/api/info", 
        "/info",
        "/api/v1/status",
        "/status"
    ]
    
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    base_url = "https://ft.10jqka.com.cn"
    
    for endpoint in info_endpoints:
        url = f"{base_url}{endpoint}"
        
        try:
            print(f"尝试: {endpoint}")
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"  ✅ 成功: {response.text[:200]}...")
            elif response.status_code == 404:
                print(f"  ❌ 端点不存在")
            else:
                print(f"  ⚠️ 其他状态: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")

def test_simple_request():
    """测试最简单的请求"""
    print("\n🧪 测试最简单的请求")
    print("=" * 50)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    # 根据文档示例，尝试最基础的请求
    url = "https://ft.10jqka.com.cn/ds_service/api/v1/real_time_quotation"
    
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    # 最简单的参数
    simple_params = {
        "codes": "300033.SZ",
        "indicators": "open,high,low,latest"
    }
    
    print(f"📊 测试URL: {url}")
    print(f"🔑 Token: {access_token[:20]}...")
    print(f"📋 参数: {simple_params}")
    
    try:
        response = requests.post(url, json=simple_params, headers=headers, timeout=30)
        
        print(f"\n📤 请求详情:")
        print(f"  方法: POST")
        print(f"  URL: {url}")
        print(f"  Headers: {headers}")
        print(f"  Body: {json.dumps(simple_params)}")
        
        print(f"\n📥 响应详情:")
        print(f"  状态码: {response.status_code}")
        print(f"  响应头: {dict(response.headers)}")
        print(f"  响应体: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"\n📊 解析结果:")
                print(f"  类型: {type(result)}")
                print(f"  键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
                print(f"  完整内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("🔍 iFinD API端点调试工具")
    print("=" * 60)
    
    # 检查Token
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    if not access_token:
        print("❌ 未找到access_token")
        return
    
    # 执行测试
    test_simple_request()
    test_different_endpoints()
    test_basic_api_info()
    
    print(f"\n{'='*60}")
    print("🏁 调试完成")
    
    print("\n💡 建议:")
    print("1. 检查iFinD官方文档获取正确的API端点")
    print("2. 确认access_token的权限和有效性")
    print("3. 联系同花顺技术支持获取准确的API文档")

if __name__ == "__main__":
    main()
