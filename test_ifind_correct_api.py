#!/usr/bin/env python3
"""
使用正确的iFinD API端点测试数据获取
基于官方文档: https://ft.10jqka.com.cn/api/v1/cmd_history_quotation
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_ifind_historical_quotation():
    """测试iFinD历史行情API"""
    print("📈 测试iFinD历史行情API")
    print("=" * 50)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    if not access_token:
        print("❌ 未找到IFIND_ACCESS_TOKEN")
        return False
    
    print(f"✅ 使用access_token: {access_token[:20]}...")
    
    # 根据官方文档的正确端点
    url = "https://ft.10jqka.com.cn/api/v1/cmd_history_quotation"
    
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    # 根据文档格式的参数
    params = {
        "codes": "000001.SZ,600036.SH",  # 平安银行,招商银行
        "indicators": "preClose,open,high,low,close,volume,amount",
        "startdate": "2025-06-01",
        "enddate": "2025-07-02"
    }
    
    print(f"📊 请求参数:")
    print(f"  URL: {url}")
    print(f"  股票代码: {params['codes']}")
    print(f"  指标: {params['indicators']}")
    print(f"  时间范围: {params['startdate']} 到 {params['enddate']}")
    
    try:
        response = requests.post(url, json=params, headers=headers, timeout=30)
        
        print(f"\n📥 响应结果:")
        print(f"  HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                api_status = result.get('status_code')
                
                print(f"  API状态码: {api_status}")
                
                if api_status == 0:
                    print("  ✅ 历史行情获取成功")
                    
                    # 解析数据
                    if 'data' in result and 'tables' in result['data']:
                        tables = result['data']['tables']
                        print(f"  数据表数量: {len(tables)}")
                        
                        for i, table in enumerate(tables):
                            if 'table' in table and table['table']:
                                data_list = table['table']
                                print(f"  表{i+1}: {len(data_list)}条数据")
                                
                                # 显示前几条数据
                                if data_list:
                                    print(f"  样本数据:")
                                    for j, data in enumerate(data_list[:3]):
                                        print(f"    {j+1}. {data}")
                    else:
                        print("  ⚠️ 响应格式异常")
                        print(f"  完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                else:
                    error_msg = result.get('reason', '未知错误')
                    print(f"  ❌ API返回错误: {error_msg}")
                    print(f"  完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
                print(f"  原始响应: {response.text}")
        else:
            print(f"  ❌ HTTP请求失败")
            print(f"  响应内容: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_ifind_basic_data():
    """测试iFinD基础数据API"""
    print("\n📊 测试iFinD基础数据API")
    print("=" * 50)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    if not access_token:
        print("❌ 未找到IFIND_ACCESS_TOKEN")
        return False
    
    # 基础数据API端点
    url = "https://ft.10jqka.com.cn/api/v1/basic_data_service"
    
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    # 根据文档示例的参数格式
    params = {
        "codes": "000001.SZ;600036.SH",  # 注意这里用分号分隔
        "indipara": [
            {
                "indicator": "ths_sq_net_asset_yield_roe_index",
                "indiparams": ["0", "101"],
                "_otherparams": {
                    "id": "680799",
                    "name": ["THSCODE", "F0", "F1", "FDIR"]
                }
            }
        ]
    }
    
    print(f"📊 请求参数:")
    print(f"  URL: {url}")
    print(f"  股票代码: {params['codes']}")
    print(f"  指标数量: {len(params['indipara'])}")
    
    try:
        response = requests.post(url, json=params, headers=headers, timeout=30)
        
        print(f"\n📥 响应结果:")
        print(f"  HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                api_status = result.get('status_code')
                
                print(f"  API状态码: {api_status}")
                
                if api_status == 0:
                    print("  ✅ 基础数据获取成功")
                    print(f"  完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                else:
                    error_msg = result.get('reason', '未知错误')
                    print(f"  ❌ API返回错误: {error_msg}")
                    print(f"  完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
                print(f"  原始响应: {response.text}")
        else:
            print(f"  ❌ HTTP请求失败")
            print(f"  响应内容: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_ifind_data_sequence():
    """测试iFinD日期序列API"""
    print("\n📅 测试iFinD日期序列API")
    print("=" * 50)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    if not access_token:
        print("❌ 未找到IFIND_ACCESS_TOKEN")
        return False
    
    # 日期序列API端点
    url = "https://ft.10jqka.com.cn/api/v1/data_sequence"
    
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    # 根据文档示例的参数格式
    params = {
        "codes": "000001.SZ;600036.SH",  # 注意这里用分号分隔
        "startdate": "2025-06-01",
        "enddate": "2025-07-02",
        "functionpara": {
            "Days": "tradedays",
            "Fill": "Previous", 
            "Interval": "D"
        },
        "indipara": [
            {
                "indicator": "ths_sq_net_asset_yield_roe_index",
                "indiparams": ["0", "101"],
                "_otherparams": {
                    "id": "680799",
                    "name": ["THSCODE", "F0", "F1", "FDIR"]
                }
            }
        ]
    }
    
    print(f"📊 请求参数:")
    print(f"  URL: {url}")
    print(f"  股票代码: {params['codes']}")
    print(f"  时间范围: {params['startdate']} 到 {params['enddate']}")
    print(f"  时间周期: {params['functionpara']['Interval']}")
    
    try:
        response = requests.post(url, json=params, headers=headers, timeout=30)
        
        print(f"\n📥 响应结果:")
        print(f"  HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                api_status = result.get('status_code')
                
                print(f"  API状态码: {api_status}")
                
                if api_status == 0:
                    print("  ✅ 日期序列数据获取成功")
                    print(f"  完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                else:
                    error_msg = result.get('reason', '未知错误')
                    print(f"  ❌ API返回错误: {error_msg}")
                    print(f"  完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
                print(f"  原始响应: {response.text}")
        else:
            print(f"  ❌ HTTP请求失败")
            print(f"  响应内容: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 iFinD HTTP API 正确端点测试")
    print("=" * 60)
    print("基于官方文档的API端点和参数格式")
    
    # 检查Token配置
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    if not access_token:
        print("❌ 未找到access_token")
        return
    
    print(f"\n🔑 Token状态: ✅ 已配置 ({access_token[:20]}...)")
    
    # 执行测试
    tests = [
        ("历史行情API", test_ifind_historical_quotation),
        ("基础数据API", test_ifind_basic_data),
        ("日期序列API", test_ifind_data_sequence),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print(f"\n{'='*60}")
    print("🏁 测试结果汇总")
    print("-" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} | {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed > 0:
        print("🎉 部分或全部API测试成功！")
        print("\n💡 下一步:")
        print("1. 根据成功的API更新iFinD数据适配器")
        print("2. 集成到TradingAgents-CN项目中")
        print("3. 在Web界面和CLI中启用iFinD数据源")
    else:
        print("⚠️ 所有API测试失败")
        print("\n💡 可能的原因:")
        print("1. access_token权限不足或已过期")
        print("2. API参数格式需要进一步调整")
        print("3. 需要联系同花顺技术支持")

if __name__ == "__main__":
    main()
