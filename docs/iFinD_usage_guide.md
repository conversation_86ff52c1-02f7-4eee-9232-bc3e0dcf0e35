# iFinD数据源使用指南

## 📊 概述

iFinD是同花顺提供的专业金融数据服务，为TradingAgents-CN项目提供机构级的数据质量和丰富的指标体系。

## 🚀 快速开始

### 1. 配置Token

在`.env`文件中配置您的iFinD Token：

```bash
# iFinD HTTP API配置
IFIND_REFRESH_TOKEN=your_refresh_token_here
IFIND_ACCESS_TOKEN=your_access_token_here  # 可选，会自动获取
```

### 2. 启动项目

```bash
# Web界面
./quick_start.sh
# 选择 1) Web界面

# CLI界面
./quick_start.sh  
# 选择 2) CLI命令行
```

## 📈 支持的指标

### 基础行情指标
- **价格数据**: 开盘价、最高价、最低价、收盘价、前收盘价
- **成交数据**: 成交量、成交额、成交笔数、换手率
- **其他**: 均价、涨跌、涨跌幅

### 估值指标
- **市值数据**: 总市值、流通市值、总股本、流通股本
- **估值比率**: PE(TTM)、PB、PS、PCF
- **分类**: A股、B股分别统计

### 交易状态
- **状态信息**: 交易状态、涨跌停状态
- **复权数据**: 复权因子
- **盘后数据**: 盘后成交量、成交额、成交笔数
- **特色指标**: 有效换手率

### 基金专用指标
- **净值数据**: 单位净值、复权净值、累计净值
- **交易数据**: 贴水、贴水率
- **仓位数据**: 估算仓位

### 债券专用指标
- **收益率**: 到期收益率
- **期限**: 剩余期限
- **久期**: 麦氏久期、修正久期
- **风险**: 凸性

### 期货期权指标
- **期货**: 前结算价、结算价、持仓量、持仓变动、振幅
- **期权**: 持仓量、持仓变动

## 🔧 高级功能

### 复权处理

支持7种复权方式：

```python
adjust_types = {
    'none': '不复权',
    'forward_reinvest': '前复权（分红再投）',
    'backward_reinvest': '后复权（分红再投）',
    'forward_cash': '前复权（现金分红）',
    'backward_cash': '后复权（现金分红）'
}
```

### 时间周期

支持多种时间周期：

```python
intervals = {
    'daily': '日线',
    'weekly': '周线', 
    'monthly': '月线',
    'quarterly': '季线',
    'yearly': '年线'
}
```

### 货币单位

支持多种货币显示：

```python
currencies = {
    'rmb': '人民币',
    'usd': '美元',
    'hkd': '港元',
    'original': '原始货币'
}
```

## 💡 使用示例

### Web界面使用

1. 启动Web界面：`./quick_start.sh` → 选择1
2. 访问：http://localhost:8501
3. 选择市场：A股
4. 输入代码：000001
5. 配置分析参数
6. 开始分析（自动使用iFinD数据）

### CLI界面使用

1. 启动CLI：`./quick_start.sh` → 选择2
2. 选择市场：A股
3. 输入代码：000001
4. 选择分析师和研究深度
5. 查看分析结果

### Python代码使用

```python
from tradingagents.dataflows.ifind_enhanced import IFindEnhancedProvider

# 创建数据提供器
provider = IFindEnhancedProvider(refresh_token)

# 获取基础数据
df = provider.get_historical_data_enhanced(
    "000001", "2025-06-01", "2025-07-02",
    indicators=['open', 'high', 'low', 'close', 'volume', 'pe_ttm'],
    adjust_type='forward_cash'
)

# 获取技术指标
df_tech = provider.get_technical_indicators(
    "000001", "2025-06-01", "2025-07-02",
    indicators=['MA', 'MACD', 'KDJ', 'RSI']
)

# 获取资金流向
df_money = provider.get_money_flow_data(
    "000001", "2025-06-01", "2025-07-02"
)
```

## 📊 数据质量

### 优势特点
- **专业数据源**: 同花顺机构级数据
- **实时更新**: T+0数据更新
- **指标丰富**: 50+ 专业指标
- **多市场**: A股、港股、基金、债券全覆盖
- **高频支持**: 分钟级数据

### 配额管理
- **行情数据**: 1.5亿次/月
- **基础数据**: 500万次/月  
- **宏观指标**: 500万次/月
- **特色数据**: 2万次/月

## ⚠️ 注意事项

### Token管理
- **refresh_token**: 长期有效，需要定期更新
- **access_token**: 7天有效，自动刷新
- **IP限制**: 单个token最多支持20个IP

### 使用建议
- **合理使用**: 注意API调用频率
- **缓存机制**: 利用项目的缓存功能
- **错误处理**: 关注API返回状态
- **网络稳定**: 确保网络连接稳定

## 🔍 故障排除

### 常见问题

1. **Token过期**
   - 检查refresh_token是否有效
   - 重新获取access_token

2. **API调用失败**
   - 检查网络连接
   - 验证Token配置
   - 查看错误日志

3. **数据获取异常**
   - 确认股票代码格式
   - 检查市场开放时间
   - 验证指标名称

### 调试方法

```bash
# 运行测试脚本
python test_ifind_enhanced_features.py

# 查看详细日志
tail -f logs/analysis.log
```

## 📞 技术支持

如遇到问题，可以：
1. 查看项目日志文件
2. 运行测试脚本诊断
3. 联系同花顺技术支持
4. 参考官方API文档

---

*本指南基于iFinD HTTP API官方文档编写，为TradingAgents-CN项目专门定制。*
