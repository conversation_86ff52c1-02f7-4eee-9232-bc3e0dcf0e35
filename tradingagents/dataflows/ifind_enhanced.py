#!/usr/bin/env python3
"""
增强版iFinD HTTP API数据提供器
基于官方indicators参数说明文档
支持技术指标、高频数据、资金流向等高级功能
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Union
import pandas as pd
from pathlib import Path
import os

class IFindEnhancedProvider:
    """增强版iFinD HTTP API数据提供器"""
    
    # 基础行情指标
    BASIC_INDICATORS = [
        'preClose', 'open', 'high', 'low', 'close', 'avgPrice',
        'change', 'changeRatio', 'volume', 'amount', 'turnoverRatio',
        'transactionAmount', 'totalShares', 'totalCapital',
        'floatSharesOfAShares', 'floatCapitalOfAShares',
        'pe_ttm', 'pe', 'pb', 'ps', 'pcf'
    ]
    
    # 技术指标
    TECHNICAL_INDICATORS = [
        'BBI', 'DDI', 'DMA', 'MA', 'EXPMA', 'MACD', 'MTM', 'PRICEOSC', 'TRIX',
        'BIAS', 'CCI', 'DBCD', 'DPO', 'KDJ', 'LWR', 'ROC', 'RSI', 'SI',
        'SRDM', 'VROC', 'VRSI', 'WR', 'ARBR', 'CR', 'PSY', 'VR', 'WAD',
        'MFI', 'OBV', 'PVT', 'WVAD', 'BBIBOLL', 'BOLL', 'CDP', 'ENV', 'MIKE',
        'LB', 'VMA', 'VMACD', 'VOSC', 'TAPI', 'VSTD', 'ADTM', 'MI', 'MICD',
        'RC', 'RCCD', 'SRMI', 'DPTB', 'JDQS', 'JDRS', 'ZDZB', 'ATR', 'MASS',
        'STD', 'VHF', 'CVLT'
    ]
    
    # 资金流向指标
    MONEY_FLOW_INDICATORS = [
        'large_amt_timeline', 'active_buy_large_volume', 'active_sell_large_volume',
        'active_buy_main_volume', 'active_sell_main_volume', 'active_buy_middle_volume',
        'active_sell_middle_volume', 'active_buy_large_amount', 'active_sell_large_amount',
        'active_buy_main_amount', 'active_sell_main_amount', 'active_buy_middle_amount',
        'active_sell_middle_amount'
    ]

    # 高频数据指标
    HIGH_FREQUENCY_INDICATORS = [
        'open', 'high', 'low', 'close', 'avgPrice', 'volume', 'amount',
        'change', 'changeRatio', 'turnoverRatio', 'sellVolume', 'buyVolume'
    ]

    # 实时行情指标
    REALTIME_INDICATORS = [
        'tradeDate', 'tradeTime', 'preClose', 'open', 'high', 'low', 'latest',
        'latestAmount', 'latestVolume', 'avgPrice', 'change', 'changeRatio',
        'upperLimit', 'downLimit', 'amount', 'volume', 'turnoverRatio',
        'sellVolume', 'buyVolume', 'totalShares', 'totalCapital', 'pb'
    ]

    # Level-2十档行情
    LEVEL2_INDICATORS = [
        'bid10', 'bid9', 'bid8', 'bid7', 'bid6', 'bid5', 'bid4', 'bid3', 'bid2', 'bid1',
        'ask1', 'ask2', 'ask3', 'ask4', 'ask5', 'ask6', 'ask7', 'ask8', 'ask9', 'ask10',
        'bidSize10', 'bidSize9', 'bidSize8', 'bidSize7', 'bidSize6', 'bidSize5',
        'bidSize4', 'bidSize3', 'bidSize2', 'bidSize1', 'askSize1', 'askSize2',
        'askSize3', 'askSize4', 'askSize5', 'askSize6', 'askSize7', 'askSize8',
        'askSize9', 'askSize10'
    ]

    # 资金流向实时指标
    REALTIME_MONEY_FLOW = [
        'mainInflow', 'mainOutflow', 'mainNetInflow', 'retailInflow', 'retailOutflow',
        'retailNetInflow', 'largeInflow', 'largeOutflow', 'largeNetInflow',
        'bigInflow', 'bigOutflow', 'bigNetInflow', 'middleInflow', 'middleOutflow',
        'middleNetInflow', 'smallInflow', 'smallOutflow', 'smallNetInflow'
    ]

    # 日内快照指标
    SNAPSHOT_INDICATORS = [
        'tradeDate', 'tradeTime', 'preClose', 'open', 'high', 'low', 'latest',
        'amt', 'vol', 'amount', 'volume', 'tradeNum', 'avgBuyPrice', 'avgSellPrice',
        'totalBuyVolume', 'totalSellVolume', 'dealDirection', 'dealtype'
    ]

    # 快照Level-2指标
    SNAPSHOT_LEVEL2 = [
        'bid10', 'bid9', 'bid8', 'bid7', 'bid6', 'bid5', 'bid4', 'bid3', 'bid2', 'bid1',
        'ask1', 'ask2', 'ask3', 'ask4', 'ask5', 'ask6', 'ask7', 'ask8', 'ask9', 'ask10',
        'bidSize10', 'bidSize9', 'bidSize8', 'bidSize7', 'bidSize6', 'bidSize5',
        'bidSize4', 'bidSize3', 'bidSize2', 'bidSize1', 'askSize1', 'askSize2',
        'askSize3', 'askSize4', 'askSize5', 'askSize6', 'askSize7', 'askSize8',
        'askSize9', 'askSize10'
    ]
    
    # 复权方式
    ADJUST_TYPES = {
        'none': 1,          # 不复权
        'forward': 2,       # 前复权（分红再投）
        'backward': 3,      # 后复权（分红再投）
        'forward_cash': 6,  # 前复权（现金分红）
        'backward_cash': 7  # 后复权（现金分红）
    }
    
    def __init__(self, refresh_token: str):
        """初始化增强版iFinD数据提供器"""
        self.refresh_token = refresh_token
        self.access_token = None
        self.access_token_expires = None
        self.base_url = "https://ft.10jqka.com.cn"
        self.session = requests.Session()
        
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept-Encoding": "gzip,deflate"
        })
        
        print("🔍 [DEBUG] 增强版iFinD数据提供器初始化完成")
    
    def _get_access_token(self, force_new: bool = False) -> str:
        """获取access_token（复用之前的逻辑）"""
        # 检查现有token是否有效
        if not force_new and self.access_token and self.access_token_expires:
            if datetime.now() < self.access_token_expires - timedelta(hours=24):
                return self.access_token
        
        # 获取新token的逻辑
        url = f"{self.base_url}/api/v1/get_access_token"
        
        methods = [
            {"headers": {"Content-Type": "application/json"}, 
             "json": {"refresh_token": self.refresh_token}},
            {"headers": {"Content-Type": "application/json", "refresh_token": self.refresh_token}}
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                if "json" in method:
                    response = self.session.post(url, headers=method["headers"], 
                                               json=method["json"], timeout=30)
                else:
                    response = self.session.post(url, headers=method["headers"], timeout=30)
                
                response.raise_for_status()
                result = response.json()
                
                if (result.get('status_code') == 0 or 
                    result.get('errorcode') == 0 or 
                    result.get('errcode') == 0):
                    
                    access_token = None
                    if 'data' in result and 'access_token' in result['data']:
                        access_token = result['data']['access_token']
                    elif 'access_token' in result:
                        access_token = result['access_token']
                    
                    if access_token:
                        self.access_token = access_token
                        self.access_token_expires = datetime.now() + timedelta(days=6)
                        return self.access_token
                        
            except Exception as e:
                continue
        
        raise Exception("所有获取access_token的方法都失败了")
    
    def get_historical_data_enhanced(self, stock_code: str, start_date: str, end_date: str,
                                   indicators: List[str] = None, adjust_type: str = 'forward',
                                   interval: str = 'D') -> pd.DataFrame:
        """
        获取增强版历史数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            indicators: 指标列表，默认使用基础指标
            adjust_type: 复权方式 ('none', 'forward', 'backward', 'forward_cash', 'backward_cash')
            interval: 时间周期 ('D'-日, 'W'-周, 'M'-月)
            
        Returns:
            pd.DataFrame: 历史数据
        """
        if indicators is None:
            indicators = self.BASIC_INDICATORS[:10]  # 使用前10个基础指标
        
        print(f"📈 [DEBUG] 获取增强历史数据: {stock_code} ({start_date} 到 {end_date})")
        print(f"📊 [DEBUG] 指标: {indicators}")
        print(f"🔧 [DEBUG] 复权方式: {adjust_type}, 周期: {interval}")
        
        # 格式化股票代码
        formatted_code = self._format_stock_code(stock_code)
        
        # 构建参数
        params = {
            "codes": formatted_code,
            "indicators": ",".join(indicators),
            "startdate": start_date,
            "enddate": end_date,
            "functionpara": {
                "CPS": self.ADJUST_TYPES.get(adjust_type, 2),
                "Interval": interval,
                "Fill": "Previous",
                "Currency": "YSHB"
            }
        }
        
        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/cmd_history_quotation"
            headers = {"Content-Type": "application/json", "access_token": access_token}
            
            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"API请求失败: {error_msg}")
                
        except Exception as e:
            print(f"❌ [DEBUG] 获取增强历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_technical_indicators(self, stock_code: str, start_date: str, end_date: str,
                               indicators: List[str] = None) -> pd.DataFrame:
        """
        获取技术指标数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            indicators: 技术指标列表
            
        Returns:
            pd.DataFrame: 技术指标数据
        """
        if indicators is None:
            indicators = ['MA', 'MACD', 'KDJ', 'RSI', 'BOLL']  # 常用技术指标
        
        print(f"📊 [DEBUG] 获取技术指标: {stock_code}")
        print(f"🔧 [DEBUG] 指标: {indicators}")
        
        return self.get_historical_data_enhanced(
            stock_code, start_date, end_date, 
            indicators=indicators, adjust_type='forward'
        )
    
    def get_money_flow_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取资金流向数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pd.DataFrame: 资金流向数据
        """
        print(f"💰 [DEBUG] 获取资金流向数据: {stock_code}")
        
        money_indicators = [
            'large_amt_timeline', 'active_buy_large_amount', 'active_sell_large_amount',
            'active_buy_main_amount', 'active_sell_main_amount'
        ]
        
        return self.get_historical_data_enhanced(
            stock_code, start_date, end_date,
            indicators=money_indicators, adjust_type='none'
        )
    
    def get_high_frequency_data(self, stock_code: str, start_time: str, end_time: str,
                              indicators: List[str] = None, interval: int = 1,
                              include_technical: bool = False) -> pd.DataFrame:
        """
        获取高频数据（分钟级）

        Args:
            stock_code: 股票代码
            start_time: 开始时间 'YYYY-MM-DD HH:mm:ss'
            end_time: 结束时间 'YYYY-MM-DD HH:mm:ss'
            indicators: 指标列表
            interval: 时间周期（1,3,5,10,15,30,60分钟）
            include_technical: 是否包含技术指标

        Returns:
            pd.DataFrame: 高频数据
        """
        if indicators is None:
            indicators = self.HIGH_FREQUENCY_INDICATORS[:8]  # 基础高频指标

        # 如果包含技术指标，添加常用技术指标
        if include_technical:
            tech_indicators = ['MA', 'MACD', 'KDJ', 'RSI']
            indicators.extend(tech_indicators)

        print(f"⚡ [DEBUG] 获取高频数据: {stock_code}")
        print(f"⏰ [DEBUG] 时间范围: {start_time} 到 {end_time}")
        print(f"📊 [DEBUG] 时间周期: {interval}分钟")
        print(f"🔧 [DEBUG] 指标: {indicators}")

        formatted_code = self._format_stock_code(stock_code)

        params = {
            "codes": formatted_code,
            "indicators": ",".join(indicators),
            "starttime": start_time,
            "endtime": end_time,
            "functionpara": {
                "Interval": str(interval),
                "Fill": "Original",
                "CPS": "no",
                "Timeformat": "BeiJingTime"
            }
        }

        # 如果包含技术指标，添加计算参数
        if include_technical:
            calculate_params = {}
            for indicator in indicators:
                if indicator in ['MA']:
                    calculate_params[indicator] = "5"  # 5日均线
                elif indicator == 'MACD':
                    calculate_params[indicator] = "12,26,9,MACD"
                elif indicator == 'KDJ':
                    calculate_params[indicator] = "9,3,3,K"
                elif indicator == 'RSI':
                    calculate_params[indicator] = "14"

            if calculate_params:
                params["functionpara"]["calculate"] = calculate_params

        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/high_frequency"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"高频数据API请求失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取高频数据失败: {e}")
            return pd.DataFrame()

    def get_realtime_quotation(self, stock_codes: Union[str, List[str]],
                             indicators: List[str] = None,
                             include_level2: bool = False,
                             include_money_flow: bool = False) -> pd.DataFrame:
        """
        获取实时行情数据

        Args:
            stock_codes: 股票代码或代码列表
            indicators: 指标列表
            include_level2: 是否包含Level-2十档行情
            include_money_flow: 是否包含资金流向

        Returns:
            pd.DataFrame: 实时行情数据
        """
        if isinstance(stock_codes, str):
            stock_codes = [stock_codes]

        if indicators is None:
            indicators = self.REALTIME_INDICATORS[:15]  # 基础实时指标

        # 添加Level-2数据
        if include_level2:
            indicators.extend(self.LEVEL2_INDICATORS)

        # 添加资金流向数据
        if include_money_flow:
            indicators.extend(self.REALTIME_MONEY_FLOW)

        print(f"📊 [DEBUG] 获取实时行情: {stock_codes}")
        print(f"🔧 [DEBUG] 指标数量: {len(indicators)}")
        print(f"📈 [DEBUG] Level-2: {include_level2}")
        print(f"💰 [DEBUG] 资金流向: {include_money_flow}")

        # 格式化股票代码
        formatted_codes = [self._format_stock_code(code) for code in stock_codes]

        params = {
            "codes": ",".join(formatted_codes),
            "indicators": ",".join(indicators)
        }

        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/real_time_quotation"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_realtime_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"实时行情API请求失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取实时行情失败: {e}")
            return pd.DataFrame()

    def get_intraday_analysis(self, stock_code: str, trade_date: str = None) -> Dict:
        """
        获取盘中分析数据

        Args:
            stock_code: 股票代码
            trade_date: 交易日期，默认为今日

        Returns:
            Dict: 盘中分析数据
        """
        if trade_date is None:
            trade_date = datetime.now().strftime("%Y-%m-%d")

        print(f"📊 [DEBUG] 获取盘中分析: {stock_code} ({trade_date})")

        # 获取实时行情
        realtime_df = self.get_realtime_quotation(
            stock_code,
            include_level2=True,
            include_money_flow=True
        )

        # 获取分钟级高频数据
        start_time = f"{trade_date} 09:15:00"
        end_time = f"{trade_date} 15:00:00"

        hf_df = self.get_high_frequency_data(
            stock_code, start_time, end_time,
            interval=1, include_technical=True
        )

        analysis = {
            'stock_code': stock_code,
            'trade_date': trade_date,
            'realtime_data': realtime_df,
            'intraday_data': hf_df,
            'analysis_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # 如果有实时数据，添加分析
        if not realtime_df.empty:
            latest = realtime_df.iloc[0]
            analysis['latest_price'] = latest.get('latest', 0)
            analysis['change_ratio'] = latest.get('changeRatio', 0)
            analysis['volume'] = latest.get('volume', 0)
            analysis['turnover_ratio'] = latest.get('turnoverRatio', 0)

            # 资金流向分析
            if 'mainNetInflow' in realtime_df.columns:
                analysis['main_net_inflow'] = latest.get('mainNetInflow', 0)
                analysis['retail_net_inflow'] = latest.get('retailNetInflow', 0)

        # 如果有高频数据，添加趋势分析
        if not hf_df.empty:
            analysis['intraday_high'] = hf_df['high'].max()
            analysis['intraday_low'] = hf_df['low'].min()
            analysis['avg_volume'] = hf_df['volume'].mean()
            analysis['data_points'] = len(hf_df)

        return analysis

    def _parse_realtime_data(self, data: Dict) -> pd.DataFrame:
        """解析实时行情数据"""
        if not data or 'tables' not in data:
            return pd.DataFrame()

        try:
            tables = data['tables']
            if not tables:
                return pd.DataFrame()

            # 实时数据通常是单行数据
            all_data = []

            for table in tables:
                if 'table' in table and table['table']:
                    table_data = table['table'][0]  # 取第一行数据
                    all_data.append(table_data)

            if all_data:
                df = pd.DataFrame(all_data)

                # 数值列转换
                numeric_columns = [col for col in df.columns
                                 if col not in ['tradeDate', 'tradeTime', 'suspensionFlag', 'tradeStatus']]
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                print(f"✅ [DEBUG] 解析实时数据成功，共{len(df)}条记录")
                return df

        except Exception as e:
            print(f"❌ [DEBUG] 解析实时数据失败: {e}")

        return pd.DataFrame()

    def get_intraday_snapshot(self, stock_codes: Union[str, List[str]],
                            start_time: str, end_time: str,
                            indicators: List[str] = None,
                            include_level2: bool = False) -> pd.DataFrame:
        """
        获取日内快照数据

        Args:
            stock_codes: 股票代码或代码列表
            start_time: 开始时间 'YYYY-MM-DD HH:mm:ss'
            end_time: 结束时间 'YYYY-MM-DD HH:mm:ss'
            indicators: 指标列表
            include_level2: 是否包含Level-2数据

        Returns:
            pd.DataFrame: 日内快照数据
        """
        if isinstance(stock_codes, str):
            stock_codes = [stock_codes]

        if indicators is None:
            indicators = self.SNAPSHOT_INDICATORS[:12]  # 基础快照指标

        # 添加Level-2数据
        if include_level2:
            indicators.extend(self.SNAPSHOT_LEVEL2)

        print(f"📸 [DEBUG] 获取日内快照: {stock_codes}")
        print(f"⏰ [DEBUG] 时间范围: {start_time} 到 {end_time}")
        print(f"🔧 [DEBUG] 指标数量: {len(indicators)}")
        print(f"📊 [DEBUG] Level-2: {include_level2}")

        # 格式化股票代码
        formatted_codes = [self._format_stock_code(code) for code in stock_codes]

        params = {
            "codes": ",".join(formatted_codes),
            "indicators": ",".join(indicators),
            "starttime": start_time,
            "endtime": end_time
        }

        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/snap_shot"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_snapshot_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"日内快照API请求失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取日内快照失败: {e}")
            return pd.DataFrame()

    def get_economic_data(self, indicators: List[str], start_date: str, end_date: str,
                         update_start_time: str = None, update_end_time: str = None) -> pd.DataFrame:
        """
        获取经济数据库数据

        Args:
            indicators: 宏观指标列表 (如 ['M001620326', 'M002822183'])
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            update_start_time: 更新起始时间 'YYYY-MM-DD HH:mm:ss'
            update_end_time: 更新结束时间 'YYYY-MM-DD HH:mm:ss'

        Returns:
            pd.DataFrame: 经济数据
        """
        print(f"📊 [DEBUG] 获取经济数据: {indicators}")
        print(f"📅 [DEBUG] 日期范围: {start_date} 到 {end_date}")

        params = {
            "indicators": ",".join(indicators),
            "startdate": start_date,
            "enddate": end_date
        }

        # 添加更新时间筛选
        if update_start_time and update_end_time:
            params["functionpara"] = {
                "startrtime": update_start_time,
                "endrtime": update_end_time
            }
            print(f"⏰ [DEBUG] 更新时间筛选: {update_start_time} 到 {update_end_time}")

        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/edb_service"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"经济数据API请求失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取经济数据失败: {e}")
            return pd.DataFrame()

    def get_thematic_report(self, report_name: str, function_params: Dict,
                          output_params: str = None) -> pd.DataFrame:
        """
        获取专题报表数据

        Args:
            report_name: 报表名称 (如 'p03341')
            function_params: 功能参数字典
            output_params: 输出参数 (如 'date:Y,thscode:Y,security_name:Y')

        Returns:
            pd.DataFrame: 专题报表数据
        """
        print(f"📋 [DEBUG] 获取专题报表: {report_name}")
        print(f"🔧 [DEBUG] 功能参数: {function_params}")

        params = {
            "reportname": report_name,
            "functionpara": function_params
        }

        if output_params:
            params["outputpara"] = output_params
            print(f"📊 [DEBUG] 输出参数: {output_params}")

        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/data_pool"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"专题报表API请求失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取专题报表失败: {e}")
            return pd.DataFrame()

    def _parse_snapshot_data(self, data: Dict) -> pd.DataFrame:
        """解析日内快照数据"""
        if not data or 'tables' not in data:
            return pd.DataFrame()

        try:
            tables = data['tables']
            if not tables:
                return pd.DataFrame()

            # 快照数据可能包含多个时间点的数据
            all_data = []

            for table in tables:
                if 'time' in table and 'table' in table:
                    time_list = table['time']
                    table_data = table['table']

                    # 为每个时间点创建数据行
                    for i, timestamp in enumerate(time_list):
                        row_data = {'timestamp': pd.to_datetime(timestamp)}

                        # 添加各个指标的数据
                        for indicator, values in table_data.items():
                            if i < len(values):
                                row_data[indicator] = values[i]

                        all_data.append(row_data)

            if all_data:
                df = pd.DataFrame(all_data)

                # 数值列转换
                numeric_columns = [col for col in df.columns if col != 'timestamp']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                print(f"✅ [DEBUG] 解析快照数据成功，共{len(df)}条记录")
                return df

        except Exception as e:
            print(f"❌ [DEBUG] 解析快照数据失败: {e}")

        return pd.DataFrame()

    def create_portfolio(self, name: str, group_id: int,
                        performance_benchmark: Dict = None,
                        info: str = "") -> Dict:
        """
        创建投资组合

        Args:
            name: 组合名称
            group_id: 所属分组ID
            performance_benchmark: 业绩基准 {'code': '000300.SH', 'name': '沪深300'}
            info: 组合说明

        Returns:
            Dict: 创建结果
        """
        print(f"💼 [DEBUG] 创建投资组合: {name}")

        if performance_benchmark is None:
            performance_benchmark = {"code": "000300.SH", "name": "沪深300"}

        params = {
            "func": "newportf",
            "name": name,
            "group": group_id,
            "performbm": performance_benchmark,
            "tday": "国内交易所",
            "currency": "CNY",
            "info": info
        }

        try:
            access_token = self._get_access_token()
            # 注意：组合管理使用不同的域名
            url = "https://quantapi.51ifind.com/api/v1/portfolio_manage"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                print(f"✅ [DEBUG] 投资组合创建成功")
                return result
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"创建投资组合失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 创建投资组合失败: {e}")
            return {}

    def portfolio_cash_operation(self, portfolio_id: int, operation_type: str,
                                amount: float, name: str = "") -> Dict:
        """
        投资组合现金存取操作

        Args:
            portfolio_id: 组合ID
            operation_type: 操作类型 ('deposit'存入, 'withdraw'取出)
            amount: 现金数额
            name: 组合名称

        Returns:
            Dict: 操作结果
        """
        print(f"💰 [DEBUG] 现金操作: {operation_type} {amount}")

        # 操作类型映射
        access_type_map = {
            'deposit': '101',  # 存入-不计入收益
            'withdraw': '102'  # 取出-不计入收益
        }

        if operation_type not in access_type_map:
            raise ValueError("操作类型必须是 'deposit' 或 'withdraw'")

        params = {
            "func": "cashacs",
            "name": name,
            "portfid": portfolio_id,
            "functionpara": {
                "acesscls": access_type_map[operation_type],
                "amount": str(amount)
            }
        }

        try:
            access_token = self._get_access_token()
            url = "https://quantapi.51ifind.com/api/v1/portfolio_manage"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                print(f"✅ [DEBUG] 现金操作成功")
                return result
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"现金操作失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 现金操作失败: {e}")
            return {}

    def portfolio_trade(self, portfolio_id: int, stock_code: str, direction: str,
                       price: float, volume: int, name: str = "",
                       code_name: str = "", market_code: str = "212100",
                       security_type: str = "001001", currency: str = "CNY") -> Dict:
        """
        投资组合交易操作

        Args:
            portfolio_id: 组合ID
            stock_code: 股票代码
            direction: 交易方向 ('buy'买入, 'sell'卖出)
            price: 成交价格
            volume: 成交数量
            name: 组合名称
            code_name: 标的名称
            market_code: 交易市场代码
            security_type: 标的类型
            currency: 结算货币

        Returns:
            Dict: 交易结果
        """
        print(f"📈 [DEBUG] 组合交易: {direction} {stock_code} {volume}@{price}")

        if direction not in ['buy', 'sell']:
            raise ValueError("交易方向必须是 'buy' 或 'sell'")

        params = {
            "func": "deal",
            "name": name,
            "portfid": portfolio_id,
            "functionpara": {
                "thscode": stock_code,
                "direct": direction,
                "codeName": code_name or stock_code,
                "marketCode": market_code,
                "securityType": security_type,
                "price": price,
                "volume": volume,
                "currency": currency,
                "fee": "0",
                "feep": 0,
                "rate": "1.00",
                "bonus": ""
            }
        }

        try:
            access_token = self._get_access_token()
            url = "https://quantapi.51ifind.com/api/v1/portfolio_manage"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                print(f"✅ [DEBUG] 组合交易成功")
                return result
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"组合交易失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 组合交易失败: {e}")
            return {}

    def get_portfolio_overview(self, portfolio_id: int, name: str = "") -> pd.DataFrame:
        """
        获取组合监控数据

        Args:
            portfolio_id: 组合ID
            name: 组合名称

        Returns:
            pd.DataFrame: 组合监控数据
        """
        print(f"📊 [DEBUG] 获取组合监控: {portfolio_id}")

        indicators = [
            "category", "thscode", "stockName", "newPrice", "increase", "increaseRate",
            "number", "marketValue", "weight", "todayProfit", "floatProfit",
            "floatProfitRate", "totalProfit", "totalProfitRate", "positionPrice"
        ]

        params = {
            "func": "query_overview",
            "name": name,
            "portfid": portfolio_id,
            "indicators": ",".join(indicators)
        }

        try:
            access_token = self._get_access_token()
            url = "https://quantapi.51ifind.com/api/v1/portfolio_manage"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"获取组合监控失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取组合监控失败: {e}")
            return pd.DataFrame()

    def get_portfolio_performance(self, portfolio_id: int, start_date: str, end_date: str,
                                benchmark: str = "000300", name: str = "",
                                perf_type: str = "utnv", cycle: str = "day") -> pd.DataFrame:
        """
        获取组合绩效指标

        Args:
            portfolio_id: 组合ID
            start_date: 开始日期
            end_date: 结束日期
            benchmark: 业绩基准
            name: 组合名称
            perf_type: 业绩类型 ('perform', 'nasset', 'utnv')
            cycle: 周期 ('day', 'week', 'month', 'year')

        Returns:
            pd.DataFrame: 绩效数据
        """
        print(f"📈 [DEBUG] 获取组合绩效: {portfolio_id} ({start_date} 到 {end_date})")

        params = {
            "func": "query_perform",
            "name": name,
            "portfid": portfolio_id,
            "performbm": benchmark,
            "startdate": start_date,
            "enddate": end_date,
            "functionpara": {
                "pfclass": perf_type,
                "cycle": cycle
            }
        }

        try:
            access_token = self._get_access_token()
            url = "https://quantapi.51ifind.com/api/v1/portfolio_manage"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"获取组合绩效失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取组合绩效失败: {e}")
            return pd.DataFrame()

    def get_portfolio_risk_metrics(self, portfolio_id: int, start_date: str, end_date: str,
                                  benchmark: str = "000300", name: str = "",
                                  cycle: str = "day") -> pd.DataFrame:
        """
        获取组合风险指标

        Args:
            portfolio_id: 组合ID
            start_date: 开始日期
            end_date: 结束日期
            benchmark: 计算基准
            name: 组合名称
            cycle: 数据频率 ('day', 'week', 'month')

        Returns:
            pd.DataFrame: 风险指标数据
        """
        print(f"⚠️ [DEBUG] 获取风险指标: {portfolio_id}")

        indicators = [
            "alpha", "yield", "annual_yield", "sharpe_ratio", "beta",
            "annual_volatility", "max_drawdown", "value_at_risk"
        ]

        params = {
            "func": "query_risk_profits",
            "name": name,
            "portfid": portfolio_id,
            "indicators": indicators,
            "startdate": start_date,
            "enddate": end_date,
            "functionpara": {
                "cycle": cycle,
                "benchmark": benchmark
            }
        }

        try:
            access_token = self._get_access_token()
            url = "https://quantapi.51ifind.com/api/v1/portfolio_manage"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"获取风险指标失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 获取风险指标失败: {e}")
            return pd.DataFrame()

    def smart_stock_picking(self, search_string: str, search_type: str = "stock") -> pd.DataFrame:
        """
        问财智能选股

        Args:
            search_string: 搜索关键词
            search_type: 搜索类别

        Returns:
            pd.DataFrame: 选股结果
        """
        print(f"🤖 [DEBUG] 智能选股: {search_string}")

        params = {
            "searchstring": search_string,
            "searchtype": search_type
        }

        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/smart_stock_picking"
            headers = {"Content-Type": "application/json", "access_token": access_token}

            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"智能选股失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] 智能选股失败: {e}")
            return pd.DataFrame()

    def get_comprehensive_analysis(self, stock_code: str, analysis_date: str = None) -> Dict:
        """
        获取综合分析数据（整合多个API）

        Args:
            stock_code: 股票代码
            analysis_date: 分析日期，默认为今日

        Returns:
            Dict: 综合分析数据
        """
        if analysis_date is None:
            analysis_date = datetime.now().strftime("%Y-%m-%d")

        print(f"🔍 [DEBUG] 获取综合分析: {stock_code} ({analysis_date})")

        analysis = {
            'stock_code': stock_code,
            'analysis_date': analysis_date,
            'analysis_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        try:
            # 1. 获取实时行情
            print("📊 获取实时行情数据...")
            realtime_df = self.get_realtime_quotation(
                stock_code, include_level2=True, include_money_flow=True
            )
            if not realtime_df.empty:
                analysis['realtime_data'] = realtime_df.to_dict('records')[0]

            # 2. 获取高频数据
            print("⚡ 获取高频数据...")
            start_time = f"{analysis_date} 09:30:00"
            end_time = f"{analysis_date} 15:00:00"

            hf_df = self.get_high_frequency_data(
                stock_code, start_time, end_time,
                interval=5, include_technical=True
            )
            if not hf_df.empty:
                analysis['high_frequency_summary'] = {
                    'data_points': len(hf_df),
                    'intraday_high': hf_df['high'].max(),
                    'intraday_low': hf_df['low'].min(),
                    'avg_volume': hf_df['volume'].mean(),
                    'latest_close': hf_df['close'].iloc[-1]
                }

            # 3. 获取日内快照（最近30分钟）
            print("📸 获取日内快照...")
            snapshot_start = f"{analysis_date} 14:30:00"
            snapshot_end = f"{analysis_date} 15:00:00"

            snapshot_df = self.get_intraday_snapshot(
                stock_code, snapshot_start, snapshot_end,
                include_level2=True
            )
            if not snapshot_df.empty:
                analysis['snapshot_summary'] = {
                    'snapshot_count': len(snapshot_df),
                    'latest_snapshot_time': snapshot_df['timestamp'].max().strftime("%H:%M:%S")
                }

            print(f"✅ [DEBUG] 综合分析完成")
            return analysis

        except Exception as e:
            print(f"❌ [DEBUG] 综合分析失败: {e}")
            analysis['error'] = str(e)
            return analysis

    def _format_stock_code(self, stock_code: str) -> str:
        """格式化股票代码"""
        code = stock_code.replace('.SZ', '').replace('.SH', '').replace('.HK', '')
        
        if len(code) == 6 and code.isdigit():
            if code.startswith(('000', '002', '300')):
                return f"{code}.SZ"
            elif code.startswith(('600', '601', '603', '688')):
                return f"{code}.SH"
            else:
                return f"{code}.SZ"
        elif len(code) <= 5 and code.isdigit():
            return f"{code.zfill(5)}.HK"
        else:
            return stock_code
    
    def _parse_enhanced_data(self, data: Dict) -> pd.DataFrame:
        """解析增强数据格式"""
        if not data or 'tables' not in data:
            return pd.DataFrame()
        
        try:
            tables = data['tables']
            if not tables:
                return pd.DataFrame()
            
            table = tables[0]
            
            if 'time' in table and 'table' in table:
                time_list = table['time']
                table_data = table['table']
                
                df_data = {}
                df_data['date'] = pd.to_datetime(time_list)
                
                for indicator, values in table_data.items():
                    df_data[indicator] = values
                
                df = pd.DataFrame(df_data)
                
                # 数值列转换
                numeric_columns = [col for col in df.columns if col != 'date']
                for col in numeric_columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                print(f"✅ [DEBUG] 解析增强数据成功，共{len(df)}条记录")
                return df
                
        except Exception as e:
            print(f"❌ [DEBUG] 解析增强数据失败: {e}")
        
        return pd.DataFrame()


def get_enhanced_stock_analysis(stock_code: str, start_date: str, end_date: str, 
                              refresh_token: str) -> str:
    """
    获取增强版股票分析报告
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        refresh_token: iFinD refresh token
        
    Returns:
        str: 增强版分析报告
    """
    try:
        provider = IFindEnhancedProvider(refresh_token)
        
        # 获取基础数据
        basic_data = provider.get_historical_data_enhanced(
            stock_code, start_date, end_date,
            indicators=['preClose', 'open', 'high', 'low', 'close', 'volume', 'amount', 
                       'turnoverRatio', 'pe_ttm', 'pb']
        )
        
        # 获取技术指标
        tech_data = provider.get_technical_indicators(
            stock_code, start_date, end_date,
            indicators=['MA', 'MACD', 'KDJ', 'RSI', 'BOLL']
        )
        
        # 获取资金流向
        money_data = provider.get_money_flow_data(stock_code, start_date, end_date)
        
        # 生成报告
        report = f"""
# {stock_code} 增强版股票分析报告 (iFinD专业数据源)

## 📊 基本信息
- 股票代码: {stock_code}
- 数据来源: 同花顺iFinD专业版
- 分析期间: {start_date} 至 {end_date}
- 数据条数: {len(basic_data)}

## 📈 基础行情数据
"""
        
        if not basic_data.empty:
            latest = basic_data.iloc[-1]
            report += f"""
- 最新收盘价: {latest.get('close', 'N/A'):.2f}
- 最新成交量: {latest.get('volume', 'N/A'):,.0f}
- 换手率: {latest.get('turnoverRatio', 'N/A'):.2f}%
- 市盈率(TTM): {latest.get('pe_ttm', 'N/A'):.2f}
- 市净率: {latest.get('pb', 'N/A'):.2f}

### 期间统计
- 最高价: {basic_data['high'].max():.2f}
- 最低价: {basic_data['low'].min():.2f}
- 平均成交量: {basic_data['volume'].mean():,.0f}
- 总成交额: {basic_data['amount'].sum():,.2f}
"""
        
        if not tech_data.empty:
            report += f"""
## 🔧 技术指标分析
- 技术指标数据条数: {len(tech_data)}
- 可用指标: {list(tech_data.columns)}
"""
        
        if not money_data.empty:
            report += f"""
## 💰 资金流向分析
- 资金流向数据条数: {len(money_data)}
- 可用指标: {list(money_data.columns)}
"""
        
        report += f"""
## 📋 数据质量评估
- ✅ 基础数据: {'完整' if not basic_data.empty else '缺失'}
- ✅ 技术指标: {'完整' if not tech_data.empty else '缺失'}
- ✅ 资金流向: {'完整' if not money_data.empty else '缺失'}

## 🎯 数据源优势
- 📊 专业级数据质量
- ⚡ 实时数据更新
- 🔧 丰富的技术指标
- 💰 详细的资金流向
- 📈 多种复权方式
- ⏰ 高频数据支持

---
*本报告由iFinD专业数据源生成，数据来源：同花顺*
"""
        
        return report
        
    except Exception as e:
        return f"❌ 增强版股票分析失败: {str(e)}"


if __name__ == "__main__":
    # 测试代码
    test_refresh_token = "your_refresh_token_here"
    print("🧪 测试增强版iFinD数据提供器")
    
    try:
        provider = IFindEnhancedProvider(test_refresh_token)
        print("✅ 增强版提供器初始化成功")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
