#!/usr/bin/env python3
"""
增强版iFinD HTTP API数据提供器
基于官方indicators参数说明文档
支持技术指标、高频数据、资金流向等高级功能
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List, Union
import pandas as pd
from pathlib import Path
import os

class IFindEnhancedProvider:
    """增强版iFinD HTTP API数据提供器"""
    
    # 基础行情指标
    BASIC_INDICATORS = [
        'preClose', 'open', 'high', 'low', 'close', 'avgPrice',
        'change', 'changeRatio', 'volume', 'amount', 'turnoverRatio',
        'transactionAmount', 'totalShares', 'totalCapital',
        'floatSharesOfAShares', 'floatCapitalOfAShares',
        'pe_ttm', 'pe', 'pb', 'ps', 'pcf'
    ]
    
    # 技术指标
    TECHNICAL_INDICATORS = [
        'BBI', 'DDI', 'DMA', 'MA', 'EXPMA', 'MACD', 'MTM', 'PRICEOSC', 'TRIX',
        'BIAS', 'CCI', 'DBCD', 'DPO', 'KDJ', 'LWR', 'ROC', 'RSI', 'SI',
        'SRDM', 'VROC', 'VRSI', 'WR', 'ARBR', 'CR', 'PSY', 'VR', 'WAD',
        'MFI', 'OBV', 'PVT', 'WVAD', 'BBIBOLL', 'BOLL', 'CDP', 'ENV', 'MIKE',
        'LB', 'VMA', 'VMACD', 'VOSC', 'TAPI', 'VSTD', 'ADTM', 'MI', 'MICD',
        'RC', 'RCCD', 'SRMI', 'DPTB', 'JDQS', 'JDRS', 'ZDZB', 'ATR', 'MASS',
        'STD', 'VHF', 'CVLT'
    ]
    
    # 资金流向指标
    MONEY_FLOW_INDICATORS = [
        'large_amt_timeline', 'active_buy_large_volume', 'active_sell_large_volume',
        'active_buy_main_volume', 'active_sell_main_volume', 'active_buy_middle_volume',
        'active_sell_middle_volume', 'active_buy_large_amount', 'active_sell_large_amount',
        'active_buy_main_amount', 'active_sell_main_amount', 'active_buy_middle_amount',
        'active_sell_middle_amount'
    ]
    
    # 复权方式
    ADJUST_TYPES = {
        'none': 1,          # 不复权
        'forward': 2,       # 前复权（分红再投）
        'backward': 3,      # 后复权（分红再投）
        'forward_cash': 6,  # 前复权（现金分红）
        'backward_cash': 7  # 后复权（现金分红）
    }
    
    def __init__(self, refresh_token: str):
        """初始化增强版iFinD数据提供器"""
        self.refresh_token = refresh_token
        self.access_token = None
        self.access_token_expires = None
        self.base_url = "https://ft.10jqka.com.cn"
        self.session = requests.Session()
        
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept-Encoding": "gzip,deflate"
        })
        
        print("🔍 [DEBUG] 增强版iFinD数据提供器初始化完成")
    
    def _get_access_token(self, force_new: bool = False) -> str:
        """获取access_token（复用之前的逻辑）"""
        # 检查现有token是否有效
        if not force_new and self.access_token and self.access_token_expires:
            if datetime.now() < self.access_token_expires - timedelta(hours=24):
                return self.access_token
        
        # 获取新token的逻辑
        url = f"{self.base_url}/api/v1/get_access_token"
        
        methods = [
            {"headers": {"Content-Type": "application/json"}, 
             "json": {"refresh_token": self.refresh_token}},
            {"headers": {"Content-Type": "application/json", "refresh_token": self.refresh_token}}
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                if "json" in method:
                    response = self.session.post(url, headers=method["headers"], 
                                               json=method["json"], timeout=30)
                else:
                    response = self.session.post(url, headers=method["headers"], timeout=30)
                
                response.raise_for_status()
                result = response.json()
                
                if (result.get('status_code') == 0 or 
                    result.get('errorcode') == 0 or 
                    result.get('errcode') == 0):
                    
                    access_token = None
                    if 'data' in result and 'access_token' in result['data']:
                        access_token = result['data']['access_token']
                    elif 'access_token' in result:
                        access_token = result['access_token']
                    
                    if access_token:
                        self.access_token = access_token
                        self.access_token_expires = datetime.now() + timedelta(days=6)
                        return self.access_token
                        
            except Exception as e:
                continue
        
        raise Exception("所有获取access_token的方法都失败了")
    
    def get_historical_data_enhanced(self, stock_code: str, start_date: str, end_date: str,
                                   indicators: List[str] = None, adjust_type: str = 'forward',
                                   interval: str = 'D') -> pd.DataFrame:
        """
        获取增强版历史数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            indicators: 指标列表，默认使用基础指标
            adjust_type: 复权方式 ('none', 'forward', 'backward', 'forward_cash', 'backward_cash')
            interval: 时间周期 ('D'-日, 'W'-周, 'M'-月)
            
        Returns:
            pd.DataFrame: 历史数据
        """
        if indicators is None:
            indicators = self.BASIC_INDICATORS[:10]  # 使用前10个基础指标
        
        print(f"📈 [DEBUG] 获取增强历史数据: {stock_code} ({start_date} 到 {end_date})")
        print(f"📊 [DEBUG] 指标: {indicators}")
        print(f"🔧 [DEBUG] 复权方式: {adjust_type}, 周期: {interval}")
        
        # 格式化股票代码
        formatted_code = self._format_stock_code(stock_code)
        
        # 构建参数
        params = {
            "codes": formatted_code,
            "indicators": ",".join(indicators),
            "startdate": start_date,
            "enddate": end_date,
            "functionpara": {
                "CPS": self.ADJUST_TYPES.get(adjust_type, 2),
                "Interval": interval,
                "Fill": "Previous",
                "Currency": "YSHB"
            }
        }
        
        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/cmd_history_quotation"
            headers = {"Content-Type": "application/json", "access_token": access_token}
            
            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"API请求失败: {error_msg}")
                
        except Exception as e:
            print(f"❌ [DEBUG] 获取增强历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_technical_indicators(self, stock_code: str, start_date: str, end_date: str,
                               indicators: List[str] = None) -> pd.DataFrame:
        """
        获取技术指标数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            indicators: 技术指标列表
            
        Returns:
            pd.DataFrame: 技术指标数据
        """
        if indicators is None:
            indicators = ['MA', 'MACD', 'KDJ', 'RSI', 'BOLL']  # 常用技术指标
        
        print(f"📊 [DEBUG] 获取技术指标: {stock_code}")
        print(f"🔧 [DEBUG] 指标: {indicators}")
        
        return self.get_historical_data_enhanced(
            stock_code, start_date, end_date, 
            indicators=indicators, adjust_type='forward'
        )
    
    def get_money_flow_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取资金流向数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pd.DataFrame: 资金流向数据
        """
        print(f"💰 [DEBUG] 获取资金流向数据: {stock_code}")
        
        money_indicators = [
            'large_amt_timeline', 'active_buy_large_amount', 'active_sell_large_amount',
            'active_buy_main_amount', 'active_sell_main_amount'
        ]
        
        return self.get_historical_data_enhanced(
            stock_code, start_date, end_date,
            indicators=money_indicators, adjust_type='none'
        )
    
    def get_high_frequency_data(self, stock_code: str, start_time: str, end_time: str,
                              indicators: List[str] = None) -> pd.DataFrame:
        """
        获取高频数据（分钟级）
        
        Args:
            stock_code: 股票代码
            start_time: 开始时间 'YYYY-MM-DD HH:mm:ss'
            end_time: 结束时间 'YYYY-MM-DD HH:mm:ss'
            indicators: 指标列表
            
        Returns:
            pd.DataFrame: 高频数据
        """
        if indicators is None:
            indicators = ['open', 'high', 'low', 'close', 'volume', 'amount']
        
        print(f"⚡ [DEBUG] 获取高频数据: {stock_code}")
        print(f"⏰ [DEBUG] 时间范围: {start_time} 到 {end_time}")
        
        formatted_code = self._format_stock_code(stock_code)
        
        params = {
            "codes": formatted_code,
            "indicators": ",".join(indicators),
            "starttime": start_time,
            "endtime": end_time,
            "functionpara": {
                "Fill": "Previous",
                "Currency": "YSHB"
            }
        }
        
        try:
            access_token = self._get_access_token()
            url = f"{self.base_url}/api/v1/high_frequency"
            headers = {"Content-Type": "application/json", "access_token": access_token}
            
            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            if result.get('errorcode') == 0:
                return self._parse_enhanced_data(result)
            else:
                error_msg = result.get('errmsg', '未知错误')
                raise Exception(f"高频数据API请求失败: {error_msg}")
                
        except Exception as e:
            print(f"❌ [DEBUG] 获取高频数据失败: {e}")
            return pd.DataFrame()
    
    def _format_stock_code(self, stock_code: str) -> str:
        """格式化股票代码"""
        code = stock_code.replace('.SZ', '').replace('.SH', '').replace('.HK', '')
        
        if len(code) == 6 and code.isdigit():
            if code.startswith(('000', '002', '300')):
                return f"{code}.SZ"
            elif code.startswith(('600', '601', '603', '688')):
                return f"{code}.SH"
            else:
                return f"{code}.SZ"
        elif len(code) <= 5 and code.isdigit():
            return f"{code.zfill(5)}.HK"
        else:
            return stock_code
    
    def _parse_enhanced_data(self, data: Dict) -> pd.DataFrame:
        """解析增强数据格式"""
        if not data or 'tables' not in data:
            return pd.DataFrame()
        
        try:
            tables = data['tables']
            if not tables:
                return pd.DataFrame()
            
            table = tables[0]
            
            if 'time' in table and 'table' in table:
                time_list = table['time']
                table_data = table['table']
                
                df_data = {}
                df_data['date'] = pd.to_datetime(time_list)
                
                for indicator, values in table_data.items():
                    df_data[indicator] = values
                
                df = pd.DataFrame(df_data)
                
                # 数值列转换
                numeric_columns = [col for col in df.columns if col != 'date']
                for col in numeric_columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                print(f"✅ [DEBUG] 解析增强数据成功，共{len(df)}条记录")
                return df
                
        except Exception as e:
            print(f"❌ [DEBUG] 解析增强数据失败: {e}")
        
        return pd.DataFrame()


def get_enhanced_stock_analysis(stock_code: str, start_date: str, end_date: str, 
                              refresh_token: str) -> str:
    """
    获取增强版股票分析报告
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        refresh_token: iFinD refresh token
        
    Returns:
        str: 增强版分析报告
    """
    try:
        provider = IFindEnhancedProvider(refresh_token)
        
        # 获取基础数据
        basic_data = provider.get_historical_data_enhanced(
            stock_code, start_date, end_date,
            indicators=['preClose', 'open', 'high', 'low', 'close', 'volume', 'amount', 
                       'turnoverRatio', 'pe_ttm', 'pb']
        )
        
        # 获取技术指标
        tech_data = provider.get_technical_indicators(
            stock_code, start_date, end_date,
            indicators=['MA', 'MACD', 'KDJ', 'RSI', 'BOLL']
        )
        
        # 获取资金流向
        money_data = provider.get_money_flow_data(stock_code, start_date, end_date)
        
        # 生成报告
        report = f"""
# {stock_code} 增强版股票分析报告 (iFinD专业数据源)

## 📊 基本信息
- 股票代码: {stock_code}
- 数据来源: 同花顺iFinD专业版
- 分析期间: {start_date} 至 {end_date}
- 数据条数: {len(basic_data)}

## 📈 基础行情数据
"""
        
        if not basic_data.empty:
            latest = basic_data.iloc[-1]
            report += f"""
- 最新收盘价: {latest.get('close', 'N/A'):.2f}
- 最新成交量: {latest.get('volume', 'N/A'):,.0f}
- 换手率: {latest.get('turnoverRatio', 'N/A'):.2f}%
- 市盈率(TTM): {latest.get('pe_ttm', 'N/A'):.2f}
- 市净率: {latest.get('pb', 'N/A'):.2f}

### 期间统计
- 最高价: {basic_data['high'].max():.2f}
- 最低价: {basic_data['low'].min():.2f}
- 平均成交量: {basic_data['volume'].mean():,.0f}
- 总成交额: {basic_data['amount'].sum():,.2f}
"""
        
        if not tech_data.empty:
            report += f"""
## 🔧 技术指标分析
- 技术指标数据条数: {len(tech_data)}
- 可用指标: {list(tech_data.columns)}
"""
        
        if not money_data.empty:
            report += f"""
## 💰 资金流向分析
- 资金流向数据条数: {len(money_data)}
- 可用指标: {list(money_data.columns)}
"""
        
        report += f"""
## 📋 数据质量评估
- ✅ 基础数据: {'完整' if not basic_data.empty else '缺失'}
- ✅ 技术指标: {'完整' if not tech_data.empty else '缺失'}
- ✅ 资金流向: {'完整' if not money_data.empty else '缺失'}

## 🎯 数据源优势
- 📊 专业级数据质量
- ⚡ 实时数据更新
- 🔧 丰富的技术指标
- 💰 详细的资金流向
- 📈 多种复权方式
- ⏰ 高频数据支持

---
*本报告由iFinD专业数据源生成，数据来源：同花顺*
"""
        
        return report
        
    except Exception as e:
        return f"❌ 增强版股票分析失败: {str(e)}"


if __name__ == "__main__":
    # 测试代码
    test_refresh_token = "your_refresh_token_here"
    print("🧪 测试增强版iFinD数据提供器")
    
    try:
        provider = IFindEnhancedProvider(test_refresh_token)
        print("✅ 增强版提供器初始化成功")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
