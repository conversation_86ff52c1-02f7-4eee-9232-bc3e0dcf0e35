#!/usr/bin/env python3
"""
iFinD HTTP API 数据提供器
同花顺iFinD数据源集成
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import pandas as pd
from pathlib import Path
import os

class IFindDataProvider:
    """iFinD HTTP API数据提供器"""
    
    def __init__(self, refresh_token: str):
        """
        初始化iFinD数据提供器
        
        Args:
            refresh_token: iFinD refresh token
        """
        self.refresh_token = refresh_token
        self.access_token = None
        self.access_token_expires = None
        self.base_url = "https://ft.10jqka.com.cn"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept-Encoding": "gzip,deflate"
        })
        
        print("🔍 [DEBUG] iFinD数据提供器初始化完成")
    
    def _get_access_token(self, force_new: bool = False) -> str:
        """
        获取access_token

        Args:
            force_new: 是否强制获取新token

        Returns:
            str: access_token
        """
        # 检查现有token是否有效
        if not force_new and self.access_token and self.access_token_expires:
            if datetime.now() < self.access_token_expires - timedelta(hours=24):
                print("🔍 [DEBUG] 使用现有access_token")
                return self.access_token

        # 选择获取方式
        if force_new:
            url = f"{self.base_url}/api/v1/update_access_token"
            print("🔍 [DEBUG] 获取新的access_token")
        else:
            url = f"{self.base_url}/api/v1/get_access_token"
            print("🔍 [DEBUG] 获取当前有效的access_token")

        # 尝试多种请求方式
        methods = [
            # 方法1: refresh_token在body中 (推荐)
            {
                "headers": {"Content-Type": "application/json"},
                "json": {"refresh_token": self.refresh_token}
            },
            # 方法2: refresh_token在header中
            {
                "headers": {
                    "Content-Type": "application/json",
                    "refresh_token": self.refresh_token
                }
            }
        ]

        for i, method in enumerate(methods, 1):
            try:
                print(f"🔍 [DEBUG] 尝试方法{i}: {method}")

                if "json" in method:
                    response = self.session.post(url, headers=method["headers"],
                                               json=method["json"], timeout=30)
                else:
                    response = self.session.post(url, headers=method["headers"], timeout=30)

                print(f"🔍 [DEBUG] 响应状态码: {response.status_code}")
                print(f"🔍 [DEBUG] 响应内容: {response.text[:200]}...")

                response.raise_for_status()
                result = response.json()

                # 检查多种可能的成功状态
                if (result.get('status_code') == 0 or
                    result.get('errorcode') == 0 or
                    result.get('errcode') == 0):

                    # 尝试从不同位置获取access_token
                    access_token = None
                    if 'data' in result and 'access_token' in result['data']:
                        access_token = result['data']['access_token']
                    elif 'access_token' in result:
                        access_token = result['access_token']

                    if access_token:
                        self.access_token = access_token
                        # 设置过期时间为6天后（提前1天刷新）
                        self.access_token_expires = datetime.now() + timedelta(days=6)

                        print(f"✅ [DEBUG] access_token获取成功: {self.access_token[:20]}...")
                        print(f"✅ [DEBUG] 过期时间: {self.access_token_expires}")
                        return self.access_token
                    else:
                        print(f"⚠️ [DEBUG] 方法{i}失败: 响应中未找到access_token")
                        continue
                else:
                    error_msg = result.get('reason') or result.get('errmsg', '未知错误')
                    print(f"⚠️ [DEBUG] 方法{i}失败: {error_msg}")
                    continue

            except Exception as e:
                print(f"⚠️ [DEBUG] 方法{i}异常: {e}")
                continue

        # 所有方法都失败
        raise Exception("所有获取access_token的方法都失败了")
    
    def _make_request(self, endpoint: str, params: Dict) -> Dict:
        """
        发送API请求

        Args:
            endpoint: API端点
            params: 请求参数

        Returns:
            Dict: 响应数据
        """
        # 确保有有效的access_token
        access_token = self._get_access_token()

        url = f"{self.base_url}{endpoint}"
        headers = {
            "Content-Type": "application/json",
            "access_token": access_token
        }

        try:
            print(f"🌐 [DEBUG] 请求iFinD API: {endpoint}")
            response = self.session.post(url, json=params, headers=headers, timeout=30)
            response.raise_for_status()

            result = response.json()

            # 新的API使用errorcode而不是status_code
            if result.get('errorcode') == 0:
                return result
            elif result.get('status_code') == 0:
                return result['data']
            else:
                error_msg = result.get('errmsg') or result.get('reason', '未知错误')
                raise Exception(f"API请求失败: {error_msg}")

        except Exception as e:
            print(f"❌ [DEBUG] API请求失败: {e}")
            raise
    
    def get_realtime_data(self, stock_code: str) -> Dict:
        """
        获取实时行情数据
        
        Args:
            stock_code: 股票代码 (如 '000001.SZ', '600036.SH')
            
        Returns:
            Dict: 实时行情数据
        """
        print(f"📊 [DEBUG] 获取实时行情: {stock_code}")
        
        # 转换股票代码格式
        formatted_code = self._format_stock_code(stock_code)
        
        endpoint = "/ds_service/api/v1/real_time_quotation"
        params = {
            "codes": formatted_code,
            "indicators": "open,high,low,latest,preClose,volume,amount,turnoverRate,pe,pb"
        }
        
        try:
            data = self._make_request(endpoint, params)
            return self._parse_realtime_data(data)
        except Exception as e:
            print(f"❌ [DEBUG] 获取实时行情失败: {e}")
            return {}
    
    def get_historical_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取历史数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'

        Returns:
            pd.DataFrame: 历史数据
        """
        print(f"📈 [DEBUG] 获取历史数据: {stock_code} ({start_date} 到 {end_date})")

        # 转换股票代码格式
        formatted_code = self._format_stock_code(stock_code)

        # 使用正确的历史行情API端点
        endpoint = "/api/v1/cmd_history_quotation"
        params = {
            "codes": formatted_code,
            "indicators": "preClose,open,high,low,close,volume,amount",
            "startdate": start_date,
            "enddate": end_date
        }

        try:
            data = self._make_request(endpoint, params)
            return self._parse_historical_data_new(data)
        except Exception as e:
            print(f"❌ [DEBUG] 获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def _format_stock_code(self, stock_code: str) -> str:
        """
        格式化股票代码为iFinD格式
        
        Args:
            stock_code: 原始股票代码
            
        Returns:
            str: 格式化后的代码
        """
        # 移除可能的后缀
        code = stock_code.replace('.SZ', '').replace('.SH', '').replace('.HK', '')
        
        # 判断市场
        if len(code) == 6 and code.isdigit():
            # A股代码
            if code.startswith(('000', '002', '300')):
                return f"{code}.SZ"  # 深交所
            elif code.startswith(('600', '601', '603', '688')):
                return f"{code}.SH"  # 上交所
            else:
                return f"{code}.SZ"  # 默认深交所
        elif len(code) <= 5 and code.isdigit():
            # 港股代码
            return f"{code.zfill(5)}.HK"
        else:
            # 其他格式，直接返回
            return stock_code
    
    def _parse_realtime_data(self, data: Dict) -> Dict:
        """解析实时数据"""
        if not data or 'tables' not in data:
            return {}
        
        try:
            table = data['tables'][0]
            if 'table' in table and table['table']:
                row = table['table'][0]
                return {
                    'open': row.get('open'),
                    'high': row.get('high'),
                    'low': row.get('low'),
                    'latest': row.get('latest'),
                    'preClose': row.get('preClose'),
                    'volume': row.get('volume'),
                    'amount': row.get('amount'),
                    'turnoverRate': row.get('turnoverRate'),
                    'pe': row.get('pe'),
                    'pb': row.get('pb')
                }
        except Exception as e:
            print(f"❌ [DEBUG] 解析实时数据失败: {e}")
        
        return {}
    
    def _parse_historical_data_new(self, data: Dict) -> pd.DataFrame:
        """解析新格式的历史数据"""
        if not data or 'tables' not in data:
            return pd.DataFrame()

        try:
            # 新的API返回格式不同，需要重新解析
            tables = data['tables']
            if not tables:
                return pd.DataFrame()

            # 取第一个股票的数据
            table = tables[0]

            if 'time' in table and 'table' in table:
                time_list = table['time']
                table_data = table['table']

                # 构建DataFrame
                df_data = {}

                # 添加日期列
                df_data['date'] = pd.to_datetime(time_list)

                # 添加各个指标列
                for indicator, values in table_data.items():
                    df_data[indicator] = values

                df = pd.DataFrame(df_data)

                # 确保数值列为float类型
                numeric_columns = ['preClose', 'open', 'high', 'low', 'close', 'volume', 'amount']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                print(f"✅ [DEBUG] 解析历史数据成功，共{len(df)}条记录")
                return df

        except Exception as e:
            print(f"❌ [DEBUG] 解析历史数据失败: {e}")

        return pd.DataFrame()

    def _parse_historical_data(self, data: Dict) -> pd.DataFrame:
        """解析历史数据（保持向后兼容）"""
        # 先尝试新格式
        result = self._parse_historical_data_new(data)
        if not result.empty:
            return result

        # 如果新格式失败，尝试旧格式
        if not data or 'tables' not in data:
            return pd.DataFrame()

        try:
            table = data['tables'][0]
            if 'table' in table and table['table']:
                df = pd.DataFrame(table['table'])

                # 转换日期格式
                if 'time' in df.columns:
                    df['date'] = pd.to_datetime(df['time'], format='%Y%m%d')
                    df = df.drop('time', axis=1)

                # 确保数值列为float类型
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                return df
        except Exception as e:
            print(f"❌ [DEBUG] 解析历史数据失败: {e}")

        return pd.DataFrame()
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 尝试获取access_token
            self._get_access_token()
            print("✅ [DEBUG] iFinD连接测试成功")
            return True
        except Exception as e:
            print(f"❌ [DEBUG] iFinD连接测试失败: {e}")
            return False


def get_ifind_stock_data(stock_code: str, start_date: str, end_date: str, refresh_token: str) -> str:
    """
    获取iFinD股票数据的主要接口函数
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        refresh_token: iFinD refresh token
        
    Returns:
        str: 格式化的股票数据
    """
    try:
        provider = IFindDataProvider(refresh_token)
        
        # 获取历史数据
        df = provider.get_historical_data(stock_code, start_date, end_date)
        
        if df.empty:
            return f"❌ 未能获取股票 {stock_code} 的历史数据"
        
        # 获取实时数据
        realtime_data = provider.get_realtime_data(stock_code)
        
        # 格式化输出
        result = f"""
# {stock_code} 股票数据分析 (iFinD数据源)

## 基本信息
- 股票代码: {stock_code}
- 数据来源: 同花顺iFinD
- 数据期间: {start_date} 至 {end_date}
- 数据条数: {len(df)}

## 实时行情
"""
        
        if realtime_data:
            result += f"""
- 最新价: {realtime_data.get('latest', 'N/A')}
- 开盘价: {realtime_data.get('open', 'N/A')}
- 最高价: {realtime_data.get('high', 'N/A')}
- 最低价: {realtime_data.get('low', 'N/A')}
- 昨收价: {realtime_data.get('preClose', 'N/A')}
- 成交量: {realtime_data.get('volume', 'N/A')}
- 成交额: {realtime_data.get('amount', 'N/A')}
- 换手率: {realtime_data.get('turnoverRate', 'N/A')}%
- 市盈率: {realtime_data.get('pe', 'N/A')}
- 市净率: {realtime_data.get('pb', 'N/A')}
"""
        
        result += f"""
## 历史数据统计
- 期间最高价: {df['high'].max():.2f}
- 期间最低价: {df['low'].min():.2f}
- 平均成交量: {df['volume'].mean():.0f}
- 总成交额: {df['amount'].sum():.2f}

## 最近5日数据
{df.tail().to_string()}
"""
        
        return result
        
    except Exception as e:
        error_msg = f"❌ iFinD数据获取失败: {str(e)}"
        print(error_msg)
        return error_msg


# 测试函数
def test_ifind_connection(refresh_token: str):
    """测试iFinD连接"""
    print("🧪 测试iFinD HTTP API连接...")
    
    try:
        provider = IFindDataProvider(refresh_token)
        success = provider.test_connection()
        
        if success:
            print("✅ iFinD HTTP API连接成功")
            
            # 测试获取数据
            test_data = provider.get_realtime_data("000001")
            if test_data:
                print("✅ 数据获取测试成功")
                print(f"测试数据: {test_data}")
            else:
                print("⚠️ 数据获取测试失败")
        else:
            print("❌ iFinD HTTP API连接失败")
            
    except Exception as e:
        print(f"❌ iFinD测试失败: {e}")


if __name__ == "__main__":
    # 测试代码
    test_refresh_token = "your_refresh_token_here"
    test_ifind_connection(test_refresh_token)
