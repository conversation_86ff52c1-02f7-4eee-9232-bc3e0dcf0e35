#!/bin/bash
# TradingAgents-CN WSL环境设置和修复脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置
CONDA_ENV_NAME="TACN"
PROJECT_DIR="/mnt/e/AI/TradingAgents-CN"
PYTHON_VERSION="3.11"

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查WSL环境
check_wsl() {
    print_header "检查WSL环境"
    
    if [[ ! -f /proc/version ]] || ! grep -q "microsoft" /proc/version; then
        print_error "此脚本需要在WSL环境中运行"
        exit 1
    fi
    
    print_success "WSL环境检查通过"
    
    # 显示系统信息
    print_info "系统信息: $(uname -a)"
    print_info "WSL版本: $(cat /proc/version | grep -o 'microsoft[^[:space:]]*')"
}

# 检查和安装conda
setup_conda() {
    print_header "检查和设置Conda"
    
    # 查找conda安装
    CONDA_PATHS=(
        "$HOME/miniconda3/etc/profile.d/conda.sh"
        "$HOME/anaconda3/etc/profile.d/conda.sh"
        "$HOME/miniconda/etc/profile.d/conda.sh"
        "/opt/miniconda3/etc/profile.d/conda.sh"
    )
    
    CONDA_SCRIPT=""
    for path in "${CONDA_PATHS[@]}"; do
        if [[ -f "$path" ]]; then
            CONDA_SCRIPT="$path"
            print_success "找到conda: $path"
            break
        fi
    done
    
    if [[ -z "$CONDA_SCRIPT" ]]; then
        print_warning "未找到conda安装"
        print_info "是否安装Miniconda? (y/n)"
        read -p "> " install_conda
        
        if [[ "$install_conda" =~ ^[Yy]$ ]]; then
            install_miniconda
        else
            print_error "需要conda才能继续"
            exit 1
        fi
    else
        # 初始化conda
        source "$CONDA_SCRIPT"
        print_success "conda初始化完成"
    fi
}

# 安装Miniconda
install_miniconda() {
    print_header "安装Miniconda"
    
    # 下载Miniconda
    MINICONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    INSTALLER="/tmp/miniconda_installer.sh"
    
    print_info "下载Miniconda安装程序..."
    if wget -O "$INSTALLER" "$MINICONDA_URL"; then
        print_success "下载完成"
    else
        print_error "下载失败"
        exit 1
    fi
    
    # 安装Miniconda
    print_info "安装Miniconda..."
    bash "$INSTALLER" -b -p "$HOME/miniconda3"
    
    # 初始化conda
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
    conda init bash
    
    print_success "Miniconda安装完成"
    print_warning "请重新启动终端或运行: source ~/.bashrc"
}

# 创建或检查conda环境
setup_conda_env() {
    print_header "设置Conda环境: $CONDA_ENV_NAME"
    
    # 检查环境是否存在
    if conda env list | grep -q "^$CONDA_ENV_NAME " || conda env list | grep -q "/.*/$CONDA_ENV_NAME"; then
        print_success "conda环境 '$CONDA_ENV_NAME' 已存在"
        
        # 激活环境
        conda activate "$CONDA_ENV_NAME" 2>/dev/null || conda activate "/home/<USER>/miniconda/envs/$CONDA_ENV_NAME" 2>/dev/null
        
        if [[ "$CONDA_DEFAULT_ENV" == *"$CONDA_ENV_NAME"* ]]; then
            print_success "环境激活成功: $CONDA_DEFAULT_ENV"
        else
            print_warning "环境激活可能失败，当前: $CONDA_DEFAULT_ENV"
        fi
    else
        print_info "创建新的conda环境..."
        conda create -n "$CONDA_ENV_NAME" python="$PYTHON_VERSION" -y
        
        if [[ $? -eq 0 ]]; then
            print_success "conda环境创建成功"
            conda activate "$CONDA_ENV_NAME"
        else
            print_error "conda环境创建失败"
            exit 1
        fi
    fi
}

# 安装Python依赖
install_dependencies() {
    print_header "安装Python依赖"
    
    # 检查项目目录
    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    cd "$PROJECT_DIR" || exit 1
    
    # 检查requirements.txt
    if [[ -f "requirements.txt" ]]; then
        print_info "安装requirements.txt中的依赖..."
        pip install -r requirements.txt
        
        if [[ $? -eq 0 ]]; then
            print_success "依赖安装完成"
        else
            print_warning "部分依赖安装失败，但继续执行"
        fi
    else
        print_warning "未找到requirements.txt文件"
        
        # 安装基础依赖
        print_info "安装基础依赖..."
        pip install streamlit pandas numpy requests python-dotenv plotly
    fi
}

# 检查API配置
check_api_config() {
    print_header "检查API配置"
    
    cd "$PROJECT_DIR" || exit 1
    
    if [[ ! -f ".env" ]]; then
        print_warning ".env文件不存在"
        
        if [[ -f ".env.example" ]]; then
            print_info "复制.env.example为.env"
            cp .env.example .env
            print_success ".env文件已创建"
        else
            print_warning "未找到.env.example文件"
        fi
        
        print_warning "请编辑.env文件，配置您的API密钥"
        print_info "主要需要配置:"
        echo "  - DASHSCOPE_API_KEY (阿里百炼)"
        echo "  - FINNHUB_API_KEY (金融数据)"
    else
        print_success ".env文件已存在"
        
        # 检查关键API密钥
        if grep -q "DASHSCOPE_API_KEY=.*[^[:space:]]" .env; then
            print_success "DASHSCOPE_API_KEY 已配置"
        else
            print_warning "DASHSCOPE_API_KEY 未配置"
        fi
        
        if grep -q "FINNHUB_API_KEY=.*[^[:space:]]" .env; then
            print_success "FINNHUB_API_KEY 已配置"
        else
            print_warning "FINNHUB_API_KEY 未配置"
        fi
    fi
}

# 测试环境
test_environment() {
    print_header "测试环境"
    
    cd "$PROJECT_DIR" || exit 1
    
    # 测试Python导入
    print_info "测试Python模块导入..."
    
    modules=("streamlit" "pandas" "numpy" "requests" "dotenv")
    for module in "${modules[@]}"; do
        if python -c "import $module" 2>/dev/null; then
            print_success "$module 导入成功"
        else
            print_error "$module 导入失败"
        fi
    done
    
    # 测试项目模块
    print_info "测试项目模块..."
    if python -c "from tradingagents.default_config import DEFAULT_CONFIG" 2>/dev/null; then
        print_success "项目模块导入成功"
    else
        print_warning "项目模块导入失败，可能需要安装额外依赖"
    fi
}

# 创建启动别名
create_aliases() {
    print_header "创建启动别名"
    
    BASHRC="$HOME/.bashrc"
    
    # 检查是否已有别名
    if grep -q "alias tacn" "$BASHRC" 2>/dev/null; then
        print_info "启动别名已存在"
    else
        print_info "添加启动别名到 ~/.bashrc"
        
        cat >> "$BASHRC" << 'EOF'

# TradingAgents-CN 快速启动别名
alias tacn='cd /mnt/e/AI/TradingAgents-CN && source ~/miniconda3/etc/profile.d/conda.sh && conda activate TACN'
alias tacn-web='tacn && python -m streamlit run web/app.py --server.port 8501 --server.address localhost'
alias tacn-cli='tacn && python cli/main.py'
alias tacn-test='tacn && python test_datasources.py'
EOF
        
        print_success "别名已添加，重新加载终端后可使用:"
        echo "  - tacn: 激活环境并进入项目目录"
        echo "  - tacn-web: 启动Web界面"
        echo "  - tacn-cli: 启动CLI界面"
        echo "  - tacn-test: 测试数据源"
    fi
}

# 主函数
main() {
    clear
    print_header "TradingAgents-CN WSL环境设置"
    echo "============================================"
    
    # 执行设置步骤
    check_wsl
    setup_conda
    setup_conda_env
    install_dependencies
    check_api_config
    test_environment
    create_aliases
    
    echo ""
    print_header "设置完成"
    print_success "环境设置完成！"
    
    echo ""
    print_info "下一步操作:"
    echo "1. 重新加载终端: source ~/.bashrc"
    echo "2. 使用快速启动: ./quick_start_wsl.sh"
    echo "3. 或使用别名: tacn-web (启动Web界面)"
    echo ""
    
    print_info "如果遇到问题，请运行: ./start_wsl.sh 进行详细诊断"
}

# 运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
