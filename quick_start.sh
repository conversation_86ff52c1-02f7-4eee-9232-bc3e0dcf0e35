#!/bin/bash
# TradingAgents-CN WSL快速启动脚本
# 简化版本，适用于已配置好环境的用户

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🚀 TradingAgents-CN WSL快速启动${NC}"
echo "=================================="

# 初始化conda
echo -e "${BLUE}📦 初始化conda环境...${NC}"

# 尝试多个可能的conda路径
CONDA_FOUND=false
CONDA_PATHS=(
    "~/miniconda3/etc/profile.d/conda.sh"
    "~/anaconda3/etc/profile.d/conda.sh"
    "~/miniconda/etc/profile.d/conda.sh"
    "/opt/miniconda3/etc/profile.d/conda.sh"
)

for conda_path in "${CONDA_PATHS[@]}"; do
    expanded_path=$(eval echo $conda_path)
    if [ -f "$expanded_path" ]; then
        echo -e "${GREEN}✅ 找到conda: $expanded_path${NC}"
        source "$expanded_path"
        CONDA_FOUND=true
        break
    fi
done

if [ "$CONDA_FOUND" = false ]; then
    echo -e "${YELLOW}⚠️ 未找到conda安装，尝试直接使用...${NC}"
fi

# 激活TACN环境
echo -e "${BLUE}🔄 激活conda环境 TACN...${NC}"

# 尝试多种方式激活环境
if conda activate /home/<USER>/miniconda/envs/TACN 2>/dev/null; then
    echo -e "${GREEN}✅ 使用完整路径激活成功${NC}"
elif conda activate TACN 2>/dev/null; then
    echo -e "${GREEN}✅ 使用环境名激活成功${NC}"
else
    echo -e "${YELLOW}⚠️ conda环境激活失败，显示可用环境:${NC}"
    conda env list 2>/dev/null || echo "无法列出conda环境"
    echo -e "${BLUE}💡 请手动激活环境: conda activate TACN${NC}"
fi

# 进入项目目录
cd /mnt/e/AI/TradingAgents-CN

# 设置Python路径
export PYTHONPATH=/mnt/e/AI/TradingAgents-CN:$PYTHONPATH
echo -e "${BLUE}🔧 设置PYTHONPATH: /mnt/e/AI/TradingAgents-CN${NC}"

# 显示当前状态
echo ""
echo -e "${GREEN}✅ 环境准备完成${NC}"
echo -e "${BLUE}📍 当前目录: $(pwd)${NC}"

# 检查Python是否可用
if command -v python &> /dev/null; then
    echo -e "${BLUE}🐍 Python版本: $(python --version 2>&1)${NC}"
else
    echo -e "${YELLOW}⚠️ Python命令不可用${NC}"
fi

# 显示conda环境状态
if [ -n "$CONDA_DEFAULT_ENV" ]; then
    echo -e "${BLUE}🌍 Conda环境: $CONDA_DEFAULT_ENV${NC}"
else
    echo -e "${YELLOW}⚠️ 未检测到活动的conda环境${NC}"
fi

# 检查关键依赖
echo -e "${BLUE}🔍 检查关键依赖...${NC}"
if python -c "import streamlit" 2>/dev/null; then
    echo -e "${GREEN}  ✅ streamlit${NC}"
else
    echo -e "${YELLOW}  ⚠️ streamlit 未安装${NC}"
fi

if python -c "import pandas" 2>/dev/null; then
    echo -e "${GREEN}  ✅ pandas${NC}"
else
    echo -e "${YELLOW}  ⚠️ pandas 未安装${NC}"
fi

echo ""
echo -e "${YELLOW}🎯 快速启动选项:${NC}"
echo "1) Web界面 (推荐)"
echo "2) CLI命令行"
echo "3) 测试数据源"
echo "4) 分析000791"
echo ""

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo -e "${GREEN}🌐 启动Web界面...${NC}"
        echo -e "${BLUE}💡 浏览器访问: http://localhost:8501${NC}"
        python -m streamlit run web/app.py --server.port 8501 --server.address localhost
        ;;
    2)
        echo -e "${GREEN}🖥️ 启动CLI界面...${NC}"
        python cli/main.py
        ;;
    3)
        echo -e "${GREEN}🔍 测试数据源...${NC}"
        python test_datasources.py
        ;;
    4)
        echo -e "${GREEN}📊 分析000791...${NC}"
        python test_logging_with_000791.py
        ;;
    *)
        echo -e "${YELLOW}⚠️ 无效选择，启动Web界面${NC}"
        python -m streamlit run web/app.py --server.port 8501 --server.address localhost
        ;;
esac
