#!/usr/bin/env python3
"""
增强的日志记录系统，用于跟踪TradingAgents-CN的分析过程
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import functools

class TradingAgentsLogger:
    """TradingAgents专用日志记录器"""
    
    def __init__(self, log_dir: str = "logs", enable_console: bool = True):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建不同类型的日志文件
        self.analysis_log = self.log_dir / "analysis.log"
        self.data_access_log = self.log_dir / "data_access.log"
        self.error_log = self.log_dir / "errors.log"
        self.audit_log = self.log_dir / "audit.log"
        
        # 设置日志格式
        self.log_format = "%(asctime)s | %(levelname)s | %(name)s | %(message)s"
        self.date_format = "%Y-%m-%d %H:%M:%S"
        
        # 创建日志记录器
        self.setup_loggers(enable_console)
        
        print(f"📝 增强日志系统已初始化，日志目录: {self.log_dir}")
    
    def setup_loggers(self, enable_console: bool):
        """设置各种日志记录器"""
        
        # 主分析日志
        self.analysis_logger = logging.getLogger("analysis")
        self.analysis_logger.setLevel(logging.DEBUG)
        
        # 数据访问日志
        self.data_logger = logging.getLogger("data_access")
        self.data_logger.setLevel(logging.DEBUG)
        
        # 错误日志
        self.error_logger = logging.getLogger("errors")
        self.error_logger.setLevel(logging.ERROR)
        
        # 审计日志
        self.audit_logger = logging.getLogger("audit")
        self.audit_logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        analysis_handler = logging.FileHandler(self.analysis_log, encoding='utf-8')
        data_handler = logging.FileHandler(self.data_access_log, encoding='utf-8')
        error_handler = logging.FileHandler(self.error_log, encoding='utf-8')
        audit_handler = logging.FileHandler(self.audit_log, encoding='utf-8')
        
        # 设置格式
        formatter = logging.Formatter(self.log_format, self.date_format)
        for handler in [analysis_handler, data_handler, error_handler, audit_handler]:
            handler.setFormatter(formatter)
        
        # 添加处理器
        self.analysis_logger.addHandler(analysis_handler)
        self.data_logger.addHandler(data_handler)
        self.error_logger.addHandler(error_handler)
        self.audit_logger.addHandler(audit_handler)
        
        # 控制台输出（可选）
        if enable_console:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_handler.setLevel(logging.INFO)
            
            self.analysis_logger.addHandler(console_handler)
    
    def log_analysis_start(self, stock_code: str, analysis_date: str, session_id: str = None):
        """记录分析开始"""
        if session_id is None:
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        message = f"🚀 分析开始 | 股票代码: {stock_code} | 分析日期: {analysis_date} | 会话ID: {session_id}"
        self.analysis_logger.info(message)
        self.audit_logger.info(f"ANALYSIS_START | {stock_code} | {session_id}")
        
        return session_id
    
    def log_analysis_end(self, stock_code: str, session_id: str, success: bool = True):
        """记录分析结束"""
        status = "成功" if success else "失败"
        message = f"🏁 分析结束 | 股票代码: {stock_code} | 会话ID: {session_id} | 状态: {status}"
        self.analysis_logger.info(message)
        self.audit_logger.info(f"ANALYSIS_END | {stock_code} | {session_id} | {status}")
    
    def log_data_access(self, stock_code: str, data_source: str, operation: str, session_id: str = None):
        """记录数据访问"""
        message = f"📊 数据访问 | 股票代码: {stock_code} | 数据源: {data_source} | 操作: {operation}"
        if session_id:
            message += f" | 会话ID: {session_id}"
        
        self.data_logger.info(message)
        self.audit_logger.info(f"DATA_ACCESS | {stock_code} | {data_source} | {operation}")
    
    def log_cache_operation(self, stock_code: str, operation: str, cache_key: str = None, session_id: str = None):
        """记录缓存操作"""
        message = f"💾 缓存操作 | 股票代码: {stock_code} | 操作: {operation}"
        if cache_key:
            message += f" | 缓存键: {cache_key}"
        if session_id:
            message += f" | 会话ID: {session_id}"
        
        self.data_logger.info(message)
    
    def log_error(self, stock_code: str, error_type: str, error_message: str, session_id: str = None):
        """记录错误"""
        message = f"❌ 错误 | 股票代码: {stock_code} | 类型: {error_type} | 消息: {error_message}"
        if session_id:
            message += f" | 会话ID: {session_id}"
        
        self.error_logger.error(message)
        self.audit_logger.error(f"ERROR | {stock_code} | {error_type}")
    
    def log_step(self, step_name: str, stock_code: str, details: str = None, session_id: str = None):
        """记录分析步骤"""
        message = f"📋 步骤: {step_name} | 股票代码: {stock_code}"
        if details:
            message += f" | 详情: {details}"
        if session_id:
            message += f" | 会话ID: {session_id}"
        
        self.analysis_logger.info(message)

def create_logging_decorator(logger_instance: TradingAgentsLogger):
    """创建日志装饰器"""
    
    def log_function_call(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 尝试从参数中提取股票代码
            stock_code = "UNKNOWN"
            session_id = None
            
            # 检查位置参数
            if args:
                if isinstance(args[0], str) and len(args[0]) == 6 and args[0].isdigit():
                    stock_code = args[0]
                elif len(args) > 1 and isinstance(args[1], str) and len(args[1]) == 6 and args[1].isdigit():
                    stock_code = args[1]
            
            # 检查关键字参数
            if 'stock_code' in kwargs:
                stock_code = kwargs['stock_code']
            elif 'symbol' in kwargs:
                stock_code = kwargs['symbol']
            
            if 'session_id' in kwargs:
                session_id = kwargs['session_id']
            
            # 记录函数调用
            logger_instance.log_step(
                step_name=func.__name__,
                stock_code=stock_code,
                details=f"调用参数: args={len(args)}, kwargs={list(kwargs.keys())}",
                session_id=session_id
            )
            
            try:
                result = func(*args, **kwargs)
                
                # 记录成功
                logger_instance.log_step(
                    step_name=f"{func.__name__}_SUCCESS",
                    stock_code=stock_code,
                    details="函数执行成功",
                    session_id=session_id
                )
                
                return result
                
            except Exception as e:
                # 记录错误
                logger_instance.log_error(
                    stock_code=stock_code,
                    error_type=type(e).__name__,
                    error_message=str(e),
                    session_id=session_id
                )
                raise
        
        return wrapper
    
    return log_function_call

def setup_enhanced_logging():
    """设置增强的日志记录系统"""
    
    # 创建日志记录器实例
    logger = TradingAgentsLogger()
    
    # 创建装饰器
    log_decorator = create_logging_decorator(logger)
    
    return logger, log_decorator

def patch_existing_functions():
    """为现有函数添加日志记录"""
    print("🔧 为现有函数添加日志记录...")

    try:
        # 导入需要监控的模块
        from tradingagents.dataflows.tdx_utils import get_china_stock_data

        # 创建日志系统
        logger, log_decorator = setup_enhanced_logging()

        # 为关键函数添加日志记录
        original_get_china_stock_data = get_china_stock_data

        @log_decorator
        def logged_get_china_stock_data(*args, **kwargs):
            return original_get_china_stock_data(*args, **kwargs)

        # 替换原函数
        import tradingagents.dataflows.tdx_utils as tdx_module
        tdx_module.get_china_stock_data = logged_get_china_stock_data

        print("✅ 日志记录系统已启用")
        return logger

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("💡 创建基础日志系统...")
        logger, _ = setup_enhanced_logging()
        return logger

def analyze_existing_logs():
    """分析现有的日志文件"""
    print("📊 分析现有日志文件")
    print("=" * 50)
    
    log_dir = Path("logs")
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return
    
    log_files = list(log_dir.glob("*.log"))
    
    if not log_files:
        print("❌ 没有找到日志文件")
        return
    
    for log_file in log_files:
        print(f"\n📄 分析日志文件: {log_file.name}")
        print("-" * 30)
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"总行数: {len(lines)}")
            
            # 分析最近的几行
            if lines:
                print("最近的日志条目:")
                for line in lines[-5:]:
                    print(f"  {line.strip()}")
        
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")

if __name__ == "__main__":
    print("🚀 TradingAgents-CN 增强日志系统")
    print("=" * 60)
    
    # 分析现有日志
    analyze_existing_logs()
    
    # 设置增强日志系统
    logger = patch_existing_functions()
    
    if logger:
        print("\n💡 使用建议:")
        print("1. 在分析开始前调用 logger.log_analysis_start()")
        print("2. 在分析结束后调用 logger.log_analysis_end()")
        print("3. 查看 logs/ 目录中的详细日志文件")
        print("4. 使用会话ID跟踪完整的分析过程")
    
    print(f"\n{'='*60}")
    print("🏁 日志系统设置完成")
