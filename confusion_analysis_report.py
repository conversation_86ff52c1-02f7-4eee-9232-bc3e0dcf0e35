#!/usr/bin/env python3
"""
TradingAgents-CN 股票代码混淆问题分析报告
专门分析000791分析时出现"酒鬼酒"的混淆问题
"""

import json
import os
from pathlib import Path
from datetime import datetime

def analyze_confusion_issue():
    """分析000791与酒鬼酒的混淆问题"""
    
    print("🔍 TradingAgents-CN 股票代码混淆问题分析报告")
    print("=" * 60)
    print("专门分析000791分析时出现'酒鬼酒'的混淆问题")
    print()
    
    # 1. 问题确认
    print("📋 1. 问题确认")
    print("-" * 30)
    
    log_file = Path("eval_results/000791/TradingAgentsStrategy_logs/full_states_log.json")
    
    if log_file.exists():
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            # 检查market_report中的股票名称
            market_report = log_data["2025-07-02"]["market_report"]
            
            if "酒鬼酒" in market_report:
                print("❌ 确认发现混淆问题！")
                print("   在000791的分析报告中发现了'酒鬼酒'的内容")
                print()
                
                # 提取相关信息
                lines = market_report.split('\n')
                for i, line in enumerate(lines):
                    if "股票名称" in line and "酒鬼酒" in line:
                        print(f"   问题行: {line.strip()}")
                        break
                
                print()
                print("📊 正确的股票信息对照:")
                print("   000791 → 甘肃电投 (或酒钢宏兴)")
                print("   000799 → 酒鬼酒")
                print("   发现：000791被错误标记为'酒鬼酒'")
                
            else:
                print("✅ 未在当前日志中发现'酒鬼酒'混淆")
        
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("❌ 000791日志文件不存在")
    
    # 2. 分析可能的原因
    print("\n🔍 2. 混淆原因分析")
    print("-" * 30)
    
    print("可能的混淆原因:")
    print("1. 股票名称映射错误")
    print("   - tdx_utils.py中的stock_names映射可能有误")
    print("   - 数据源返回了错误的股票名称")
    print()
    
    print("2. 缓存数据交叉污染")
    print("   - 000791和000799的缓存数据可能混淆")
    print("   - 数据获取时发生了代码替换")
    print()
    
    print("3. API调用参数错误")
    print("   - 调用数据API时传入了错误的股票代码")
    print("   - 股票代码在传递过程中被修改")
    print()
    
    print("4. 字符串相似性导致的错误匹配")
    print("   - 000791 vs 000799 (数字相近)")
    print("   - 都包含'酒'字，可能触发了错误的匹配逻辑")
    
    # 3. 检查股票名称映射
    print("\n📋 3. 检查股票名称映射")
    print("-" * 30)
    
    tdx_utils_path = Path("tradingagents/dataflows/tdx_utils.py")
    if tdx_utils_path.exists():
        try:
            with open(tdx_utils_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "stock_names" in content:
                print("✅ 找到stock_names映射")
                
                # 检查000791和000799的映射
                if "'000791'" in content:
                    print("✅ 000791在映射中存在")
                else:
                    print("⚠️  000791不在映射中")
                
                if "'000799'" in content:
                    print("✅ 000799在映射中存在")
                else:
                    print("⚠️  000799不在映射中")
                
                # 查找酒鬼酒相关映射
                if "酒鬼酒" in content:
                    print("⚠️  发现'酒鬼酒'在映射中")
                    lines = content.split('\n')
                    for line in lines:
                        if "酒鬼酒" in line:
                            print(f"   映射行: {line.strip()}")
            else:
                print("❌ 未找到stock_names映射")
        
        except Exception as e:
            print(f"❌ 读取tdx_utils.py失败: {e}")
    else:
        print("❌ tdx_utils.py文件不存在")
    
    # 4. 检查缓存数据
    print("\n💾 4. 检查缓存数据")
    print("-" * 30)
    
    cache_dir = Path("tradingagents/dataflows/data_cache")
    if cache_dir.exists():
        stock_cache_dir = cache_dir / "stock_data"
        metadata_dir = cache_dir / "metadata"
        
        # 检查000791的缓存文件
        if stock_cache_dir.exists():
            cache_791_files = list(stock_cache_dir.glob("*000791*.txt"))
            cache_799_files = list(stock_cache_dir.glob("*000799*.txt"))
            
            print(f"000791缓存文件数量: {len(cache_791_files)}")
            print(f"000799缓存文件数量: {len(cache_799_files)}")
            
            # 检查000791缓存文件内容
            for cache_file in cache_791_files:
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if "酒鬼酒" in content:
                        print(f"❌ 发现混淆！文件 {cache_file.name} 包含'酒鬼酒'")
                        
                        # 显示具体的混淆行
                        lines = content.split('\n')
                        for i, line in enumerate(lines, 1):
                            if "酒鬼酒" in line:
                                print(f"   第{i}行: {line.strip()}")
                    else:
                        print(f"✅ 文件 {cache_file.name} 内容正常")
                
                except Exception as e:
                    print(f"❌ 读取缓存文件 {cache_file.name} 失败: {e}")
    
    # 5. 生成修复建议
    print("\n🔧 5. 修复建议")
    print("-" * 30)
    
    print("立即修复措施:")
    print("1. 清理缓存数据")
    print("   rm -rf tradingagents/dataflows/data_cache/*000791*")
    print("   rm -rf tradingagents/dataflows/data_cache/*000799*")
    print()
    
    print("2. 检查并修正股票名称映射")
    print("   在tdx_utils.py中确保:")
    print("   '000791': '甘肃电投'  # 或'酒钢宏兴'")
    print("   '000799': '酒鬼酒'")
    print()
    
    print("3. 添加股票代码验证")
    print("   在数据获取函数中添加代码验证逻辑")
    print("   确保返回的数据与请求的股票代码一致")
    print()
    
    print("长期改进措施:")
    print("1. 实现数据一致性检查")
    print("2. 添加股票代码-名称映射验证")
    print("3. 增强日志记录，便于问题追踪")
    print("4. 实现缓存数据的完整性校验")
    
    # 6. 生成修复脚本
    print("\n📝 6. 生成修复脚本")
    print("-" * 30)
    
    fix_script = """#!/bin/bash
# 修复000791股票代码混淆问题

echo "🔧 开始修复000791股票代码混淆问题..."

# 1. 清理相关缓存
echo "1. 清理缓存数据..."
rm -rf tradingagents/dataflows/data_cache/stock_data/*000791*
rm -rf tradingagents/dataflows/data_cache/stock_data/*000799*
rm -rf tradingagents/dataflows/data_cache/metadata/*000791*
rm -rf tradingagents/dataflows/data_cache/metadata/*000799*

# 2. 清理结果目录
echo "2. 清理结果目录..."
rm -rf eval_results/000791
rm -rf eval_results/000799

echo "✅ 缓存清理完成"

# 3. 重新获取正确数据
echo "3. 建议重新运行分析以获取正确数据"
echo "   python cli/main.py --stock 000791"

echo "🏁 修复脚本执行完成"
"""
    
    with open("fix_confusion.sh", 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 修复脚本已生成: fix_confusion.sh")
    print("   运行: chmod +x fix_confusion.sh && ./fix_confusion.sh")

def generate_prevention_measures():
    """生成预防措施"""
    
    print("\n🛡️  7. 预防措施")
    print("-" * 30)
    
    prevention_code = '''
# 在数据获取函数中添加验证逻辑
def validate_stock_data(stock_code, stock_data):
    """验证股票数据的一致性"""
    
    # 检查股票代码是否匹配
    if stock_code in stock_data and stock_code not in stock_data:
        raise ValueError(f"股票代码不匹配: 请求{stock_code}, 返回数据中未找到")
    
    # 检查股票名称映射
    expected_names = {
        '000791': ['甘肃电投', '酒钢宏兴'],
        '000799': ['酒鬼酒']
    }
    
    if stock_code in expected_names:
        for line in stock_data.split('\\n'):
            if '股票名称' in line:
                for expected_name in expected_names[stock_code]:
                    if expected_name in line:
                        return True
                
                # 如果找到了股票名称行但不匹配预期
                raise ValueError(f"股票名称不匹配: {stock_code} 应该是 {expected_names[stock_code]}")
    
    return True

# 在缓存保存前调用验证
def save_stock_data_with_validation(stock_code, data):
    """保存股票数据前进行验证"""
    validate_stock_data(stock_code, data)
    # 然后保存数据...
'''
    
    print("建议在代码中添加以下验证逻辑:")
    print(prevention_code)

if __name__ == "__main__":
    analyze_confusion_issue()
    generate_prevention_measures()
    
    print(f"\n{'='*60}")
    print("🏁 混淆问题分析完成")
    print()
    print("📋 总结:")
    print("- ❌ 确认发现000791被错误标记为'酒鬼酒'")
    print("- 🔍 问题可能源于股票名称映射错误或缓存污染")
    print("- 🔧 已提供修复脚本和预防措施")
    print("- 💡 建议立即清理缓存并重新分析")
