#!/bin/bash
# 修复000791股票代码混淆问题

echo "🔧 开始修复000791股票代码混淆问题..."

# 1. 清理相关缓存
echo "1. 清理缓存数据..."
rm -rf tradingagents/dataflows/data_cache/stock_data/*000791*
rm -rf tradingagents/dataflows/data_cache/stock_data/*000799*
rm -rf tradingagents/dataflows/data_cache/metadata/*000791*
rm -rf tradingagents/dataflows/data_cache/metadata/*000799*

# 2. 清理结果目录
echo "2. 清理结果目录..."
rm -rf eval_results/000791
rm -rf eval_results/000799

echo "✅ 缓存清理完成"

# 3. 重新获取正确数据
echo "3. 建议重新运行分析以获取正确数据"
echo "   python cli/main.py --stock 000791"

echo "🏁 修复脚本执行完成"
