# TradingAgents-CN WSL启动指南

本指南介绍如何在Windows WSL环境中使用TradingAgents-CN项目的启动脚本。

## 📋 脚本概览

我们为WSL环境提供了多个启动脚本，满足不同的使用需求：

| 脚本文件 | 用途 | 适用场景 |
|---------|------|----------|
| `start_wsl.bat` | Windows批处理启动器 | 从Windows直接启动WSL脚本 |
| `setup_wsl_env.sh` | 环境设置脚本 | 首次使用或环境修复 |
| `quick_start_wsl.sh` | 快速启动脚本 | 日常使用，环境已配置 |
| `start_wsl.sh` | 完整启动脚本 | 高级用户，包含诊断工具 |

## 🚀 快速开始

### 方法1: Windows批处理启动 (推荐)

```cmd
# 在Windows命令行或PowerShell中运行
start_wsl.bat
```

这将显示一个菜单，让您选择不同的启动方式。

### 方法2: 直接在WSL中运行

```bash
# 在WSL终端中运行
cd /mnt/e/AI/TradingAgents-CN

# 快速启动 (推荐)
./quick_start_wsl.sh

# 或完整启动
./start_wsl.sh
```

## 🔧 首次设置

如果是第一次使用或遇到环境问题，请运行环境设置脚本：

```bash
# 在WSL中运行
cd /mnt/e/AI/TradingAgents-CN
./setup_wsl_env.sh
```

这个脚本会：
- ✅ 检查WSL环境
- ✅ 安装/配置Conda
- ✅ 创建TACN虚拟环境
- ✅ 安装Python依赖
- ✅ 检查API配置
- ✅ 创建便捷别名

## 📱 启动选项说明

### 1. Web界面 (推荐)
- 启动Streamlit Web应用
- 浏览器访问: http://localhost:8501
- 提供图形化的股票分析界面
- 支持实时进度显示

### 2. CLI命令行
- 启动交互式命令行界面
- 适合高级用户和自动化脚本
- 支持批量分析

### 3. 测试数据源
- 检查各种数据源连接状态
- 验证API配置
- 测试通达信、Yahoo Finance等

### 4. 分析000791
- 运行增强日志系统测试
- 专门测试000791股票分析
- 检查日志记录功能

## 🛠️ 环境要求

### 系统要求
- Windows 10/11 with WSL2
- Ubuntu 20.04+ (或其他Linux发行版)
- 4GB+ RAM (推荐8GB+)

### 软件要求
- Python 3.10+ (推荐3.11)
- Conda或Miniconda
- Git

### 网络要求
- 稳定的互联网连接
- 访问金融数据API的能力

## 🔑 API配置

在使用前，请确保配置了必要的API密钥：

```bash
# 编辑.env文件
nano .env

# 主要配置项
DASHSCOPE_API_KEY=your_dashscope_api_key    # 阿里百炼 (必需)
FINNHUB_API_KEY=your_finnhub_api_key        # 金融数据 (推荐)
GOOGLE_API_KEY=your_google_api_key          # Google AI (可选)
```

## 🎯 便捷别名

环境设置完成后，您可以使用以下别名快速启动：

```bash
# 激活环境并进入项目目录
tacn

# 启动Web界面
tacn-web

# 启动CLI界面
tacn-cli

# 测试数据源
tacn-test
```

## 🔍 故障排除

### 常见问题1: Conda环境激活失败

**症状**: 显示"conda环境激活失败"

**解决方案**:
```bash
# 重新运行环境设置
./setup_wsl_env.sh

# 或手动激活
source ~/miniconda3/etc/profile.d/conda.sh
conda activate TACN
```

### 常见问题2: Python模块导入失败

**症状**: 显示"streamlit 未安装"等错误

**解决方案**:
```bash
# 确保在正确的conda环境中
conda activate TACN

# 重新安装依赖
pip install -r requirements.txt
```

### 常见问题3: 端口被占用

**症状**: Web界面启动失败，端口8501被占用

**解决方案**:
```bash
# 查找占用端口的进程
netstat -tuln | grep 8501

# 使用其他端口
python -m streamlit run web/app.py --server.port 8502
```

### 常见问题4: API密钥错误

**症状**: 分析时显示API错误

**解决方案**:
```bash
# 检查.env文件
cat .env

# 确保API密钥格式正确，无多余空格
# 重新启动应用使配置生效
```

## 📊 性能优化

### 内存优化
```bash
# 如果内存不足，可以限制并发
export STREAMLIT_SERVER_MAX_UPLOAD_SIZE=200
export STREAMLIT_SERVER_MAX_MESSAGE_SIZE=200
```

### 网络优化
```bash
# 设置代理（如果需要）
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port
```

## 📝 日志和调试

### 查看日志
```bash
# 查看应用日志
tail -f logs/analysis.log

# 查看错误日志
tail -f logs/errors.log

# 查看审计日志
tail -f logs/audit.log
```

### 启用调试模式
```bash
# 设置调试级别
export TRADINGAGENTS_LOG_LEVEL=DEBUG

# 启动时显示详细信息
./start_wsl.sh
```

## 🔄 更新和维护

### 更新项目
```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade
```

### 清理缓存
```bash
# 清理数据缓存
rm -rf tradingagents/dataflows/data_cache/*

# 清理Python缓存
find . -name "__pycache__" -type d -exec rm -rf {} +
```

## 📞 获取帮助

如果遇到问题，可以：

1. **查看详细日志**: 运行 `./start_wsl.sh` 选择"6) 检查系统状态"
2. **运行诊断**: 使用 `./setup_wsl_env.sh` 重新配置环境
3. **查看文档**: 阅读 `docs/` 目录下的详细文档
4. **提交Issue**: 在GitHub上提交问题报告

## 🎉 成功标志

当您看到以下信息时，说明环境配置成功：

```
✅ 环境准备完成
📍 当前目录: /mnt/e/AI/TradingAgents-CN
🐍 Python版本: Python 3.11.13
🌍 Conda环境: TACN
🔍 检查关键依赖...
  ✅ streamlit
  ✅ pandas
```

现在您可以开始使用TradingAgents-CN进行股票分析了！

---

**提示**: 建议将此指南保存为书签，以便随时查阅。
