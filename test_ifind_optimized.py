#!/usr/bin/env python3
"""
测试iFinD优化后的智能选股和组合监控功能
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 加载环境变量
load_dotenv()

def test_api_connectivity():
    """测试API连接性"""
    print("🔍 测试API连接性和权限")
    print("=" * 40)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token:
        print("❌ 未找到IFIND_REFRESH_TOKEN")
        return False
    
    try:
        from tradingagents.dataflows.ifind_enhanced import IFindEnhancedProvider
        
        provider = IFindEnhancedProvider(refresh_token)
        
        # 设置access_token（如果有）
        access_token = os.getenv('IFIND_ACCESS_TOKEN')
        if access_token:
            provider.access_token = access_token
        
        # 执行连接性测试
        results = provider.test_api_connectivity()
        
        print("📊 API连接性测试结果:")
        print(f"  Token有效性: {'✅' if results['token_valid'] else '❌'}")
        print(f"  历史数据API: {'✅' if results['historical_data'] else '❌'}")
        print(f"  实时数据API: {'✅' if results['realtime_data'] else '❌'}")
        print(f"  组合管理API: {'✅' if results['portfolio_manage'] else '❌'}")
        print(f"  智能选股API: {'✅' if results['smart_picking'] else '❌'}")
        
        if results['errors']:
            print("\n⚠️ 发现的问题:")
            for error in results['errors']:
                print(f"  - {error}")
        
        return results['token_valid']
        
    except Exception as e:
        print(f"❌ API连接性测试失败: {e}")
        return False

def test_optimized_smart_picking():
    """测试优化后的智能选股"""
    print("\n🤖 测试优化后的智能选股")
    print("=" * 40)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token:
        print("❌ 未找到IFIND_REFRESH_TOKEN")
        return False
    
    try:
        from tradingagents.dataflows.ifind_enhanced import IFindEnhancedProvider
        
        provider = IFindEnhancedProvider(refresh_token)
        
        # 设置access_token（如果有）
        access_token = os.getenv('IFIND_ACCESS_TOKEN')
        if access_token:
            provider.access_token = access_token
        
        # 测试不同复杂度的查询
        test_queries = [
            "银行",  # 简单行业查询
            "A股",   # 基础市场查询
            "市盈率<30",  # 简单数值条件
            "银行股",  # 行业股票
            "市盈率小于20的银行股"  # 复杂查询
        ]
        
        success_count = 0
        
        for query in test_queries:
            print(f"\n🔍 测试查询: {query}")
            
            try:
                df = provider.smart_stock_picking(query)
                
                if not df.empty:
                    print(f"✅ 查询成功: 找到{len(df)}个结果")
                    print(f"   结果列: {list(df.columns)}")
                    
                    # 显示前3个结果
                    if len(df) > 0:
                        print("   前3个结果:")
                        for i in range(min(3, len(df))):
                            row = df.iloc[i]
                            print(f"     {i+1}. {row.to_dict()}")
                    
                    success_count += 1
                else:
                    print(f"⚠️ 查询无结果")
                    
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        
        print(f"\n📊 智能选股测试结果: {success_count}/{len(test_queries)} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 智能选股测试失败: {e}")
        return False

def test_portfolio_status_check():
    """测试组合状态检查"""
    print("\n📊 测试组合状态检查")
    print("=" * 40)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token:
        print("❌ 未找到IFIND_REFRESH_TOKEN")
        return False
    
    try:
        from tradingagents.dataflows.ifind_enhanced import IFindEnhancedProvider
        
        provider = IFindEnhancedProvider(refresh_token)
        
        # 设置access_token（如果有）
        access_token = os.getenv('IFIND_ACCESS_TOKEN')
        if access_token:
            provider.access_token = access_token
        
        # 检查之前创建的组合状态
        portfolio_id = 371486
        portfolio_name = "TradingAgents测试组合"
        
        print(f"🔍 检查组合状态: {portfolio_name} (ID: {portfolio_id})")
        
        status = provider.check_portfolio_status(portfolio_id, portfolio_name)
        
        print("📋 组合状态信息:")
        print(f"  组合ID: {status.get('portfolio_id')}")
        print(f"  组合存在: {'✅' if status.get('exists') else '❌'}")
        print(f"  有持仓: {'✅' if status.get('has_positions') else '❌'}")
        print(f"  持仓数量: {status.get('position_count', 0)}")
        print(f"  错误码: {status.get('error_code', 'N/A')}")
        print(f"  错误信息: {status.get('error_message', 'N/A')}")
        
        if 'error' in status:
            print(f"  异常信息: {status['error']}")
        
        return status.get('exists', False)
        
    except Exception as e:
        print(f"❌ 组合状态检查失败: {e}")
        return False

def test_optimized_portfolio_monitoring():
    """测试优化后的组合监控"""
    print("\n📊 测试优化后的组合监控")
    print("=" * 40)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token:
        print("❌ 未找到IFIND_REFRESH_TOKEN")
        return False
    
    try:
        from tradingagents.dataflows.ifind_enhanced import IFindEnhancedProvider
        
        provider = IFindEnhancedProvider(refresh_token)
        
        # 设置access_token（如果有）
        access_token = os.getenv('IFIND_ACCESS_TOKEN')
        if access_token:
            provider.access_token = access_token
        
        portfolio_id = 371486
        portfolio_name = "TradingAgents测试组合"
        
        print(f"📊 测试组合监控: {portfolio_name} (ID: {portfolio_id})")
        
        # 使用优化后的组合监控功能
        df = provider.get_portfolio_overview(portfolio_id, portfolio_name)
        
        if not df.empty:
            print(f"✅ 组合监控成功: {len(df)}条持仓记录")
            print(f"   可用列: {list(df.columns)}")
            
            # 显示持仓摘要
            print("\n📋 持仓摘要:")
            print(df.to_string())
            
            # 计算组合统计
            if 'marketValue' in df.columns:
                total_value = df['marketValue'].sum()
                print(f"\n💰 组合总市值: {total_value:,.2f}")
            
            if 'floatProfit' in df.columns:
                total_profit = df['floatProfit'].sum()
                print(f"💹 总浮动盈亏: {total_profit:,.2f}")
            
            return True
        else:
            print("⚠️ 组合监控返回空数据")
            print("💡 可能原因:")
            print("  1. 组合中没有持仓")
            print("  2. 交易尚未结算")
            print("  3. 权限不足")
            return False
        
    except Exception as e:
        print(f"❌ 组合监控测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("\n⚠️ 测试错误处理机制")
    print("=" * 40)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token:
        print("❌ 未找到IFIND_REFRESH_TOKEN")
        return False
    
    try:
        from tradingagents.dataflows.ifind_enhanced import IFindEnhancedProvider
        
        provider = IFindEnhancedProvider(refresh_token)
        
        # 设置access_token（如果有）
        access_token = os.getenv('IFIND_ACCESS_TOKEN')
        if access_token:
            provider.access_token = access_token
        
        # 测试错误处理
        print("🔍 测试错误码处理:")
        
        test_errors = [
            (-4001, "no data"),
            (-1202, "parameter error"),
            (-4100, "login required"),
            (-4400, "rate limit exceeded")
        ]
        
        for error_code, error_msg in test_errors:
            explanation = provider._handle_api_error(error_code, error_msg)
            print(f"  {error_code}: {explanation}")
        
        print("✅ 错误处理机制正常")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 iFinD优化功能测试套件")
    print("=" * 60)
    print("测试智能选股和组合监控的优化效果")
    
    # 执行测试
    tests = [
        ("API连接性测试", test_api_connectivity),
        ("优化智能选股", test_optimized_smart_picking),
        ("组合状态检查", test_portfolio_status_check),
        ("优化组合监控", test_optimized_portfolio_monitoring),
        ("错误处理机制", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print(f"\n{'='*60}")
    print("🏁 优化功能测试结果汇总")
    print("-" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} | {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed >= total * 0.6:  # 60%以上通过认为优化成功
        print("🎉 iFinD功能优化成功！")
        print("\n💡 优化成果:")
        print("  ✅ 智能选股查询策略优化")
        print("  ✅ 组合监控参数格式优化")
        print("  ✅ 错误处理机制完善")
        print("  ✅ API连接性诊断功能")
    else:
        print("⚠️ 部分功能仍需进一步优化")
        print("\n💡 建议:")
        print("  1. 检查Token权限范围")
        print("  2. 确认组合中有实际持仓")
        print("  3. 联系同花顺技术支持")

if __name__ == "__main__":
    main()
