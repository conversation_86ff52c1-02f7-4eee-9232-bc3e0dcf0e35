
## 🚀 TradingAgents-CN 启动脚本修复说明

### ✅ 修复内容
1. **PYTHONPATH设置**: 自动设置项目根目录到Python路径
2. **CLI启动方式**: 使用 `python -m cli.main` 替代直接调用
3. **选项简化**: 移除000791分析选项，保留核心功能
4. **错误处理**: 添加更好的错误提示和调试信息

### 📋 使用方法

#### 方法1: 使用修复后的启动脚本 (推荐)
```bash
./quick_start.sh
```

#### 方法2: 手动设置环境
```bash
# 设置PYTHONPATH
export PYTHONPATH=/mnt/e/AI/TradingAgents-CN:$PYTHONPATH

# 启动CLI
python -m cli.main

# 或启动Web界面
python -m streamlit run web/app.py
```

#### 方法3: 在项目根目录直接运行
```bash
cd /mnt/e/AI/TradingAgents-CN
python -m cli.main
```

### 🎯 启动选项说明

1. **Web界面 (推荐)**
   - 启动Streamlit Web应用
   - 浏览器访问: http://localhost:8501
   - 图形化界面，易于使用

2. **CLI命令行**
   - 交互式命令行界面
   - 适合高级用户和脚本化操作
   - 支持批量分析

3. **测试数据源**
   - 检查各种数据源连接状态
   - 验证API配置
   - 测试通达信、Yahoo Finance等

### ⚠️ 故障排除

如果仍然遇到模块导入错误：

1. **检查conda环境**:
   ```bash
   conda activate TACN
   which python
   ```

2. **手动设置PYTHONPATH**:
   ```bash
   export PYTHONPATH=/mnt/e/AI/TradingAgents-CN:$PYTHONPATH
   echo $PYTHONPATH
   ```

3. **验证模块导入**:
   ```bash
   python -c "from tradingagents.default_config import DEFAULT_CONFIG; print('导入成功')"
   ```

4. **使用绝对路径**:
   ```bash
   cd /mnt/e/AI/TradingAgents-CN
   /home/<USER>/miniconda/envs/TACN/bin/python -m cli.main
   ```

### 💡 最佳实践

1. **始终在项目根目录运行脚本**
2. **确保conda环境已激活**
3. **使用模块方式运行Python脚本**
4. **检查PYTHONPATH设置**
