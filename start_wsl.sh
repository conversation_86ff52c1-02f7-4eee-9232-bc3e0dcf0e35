#!/bin/bash
# TradingAgents-CN WSL环境启动脚本
# 适用于Windows WSL (Windows Subsystem for Linux) 环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="TradingAgents-CN"
PROJECT_DIR="/mnt/e/AI/TradingAgents-CN"
CONDA_ENV_NAME="TACN"
WEB_PORT="8501"

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# 函数：检查WSL环境
check_wsl_environment() {
    print_info "检查WSL环境..."
    
    # 检查是否在WSL中运行
    if [[ ! -f /proc/version ]] || ! grep -q "microsoft" /proc/version; then
        print_error "此脚本需要在WSL环境中运行"
        exit 1
    fi
    
    print_success "WSL环境检查通过"
    
    # 显示WSL版本信息
    if command -v wsl.exe &> /dev/null; then
        WSL_VERSION=$(wsl.exe --version 2>/dev/null | head -1 || echo "WSL 1")
        print_info "WSL版本: $WSL_VERSION"
    fi
}

# 函数：初始化conda环境
init_conda() {
    print_info "初始化conda环境..."
    
    # 查找conda安装路径
    CONDA_PATHS=(
        "$HOME/miniconda3"
        "$HOME/anaconda3"
        "$HOME/miniconda"
        "/opt/miniconda3"
        "/opt/anaconda3"
    )
    
    CONDA_PATH=""
    for path in "${CONDA_PATHS[@]}"; do
        if [[ -f "$path/etc/profile.d/conda.sh" ]]; then
            CONDA_PATH="$path"
            break
        fi
    done
    
    if [[ -z "$CONDA_PATH" ]]; then
        print_error "未找到conda安装，请先安装Miniconda或Anaconda"
        print_info "安装命令："
        echo "  wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
        echo "  bash Miniconda3-latest-Linux-x86_64.sh"
        exit 1
    fi
    
    # 初始化conda
    source "$CONDA_PATH/etc/profile.d/conda.sh"
    print_success "conda环境初始化完成: $CONDA_PATH"
}

# 函数：检查并激活conda环境
activate_conda_env() {
    print_info "检查conda环境: $CONDA_ENV_NAME"
    
    # 检查环境是否存在
    if ! conda env list | grep -q "^$CONDA_ENV_NAME "; then
        print_warning "conda环境 '$CONDA_ENV_NAME' 不存在"
        print_info "可用的conda环境："
        conda env list
        
        read -p "是否创建新的conda环境? (y/n): " create_env
        if [[ "$create_env" =~ ^[Yy]$ ]]; then
            create_conda_env
        else
            print_error "需要conda环境才能继续"
            exit 1
        fi
    fi
    
    # 激活环境
    print_info "激活conda环境: $CONDA_ENV_NAME"
    conda activate "$CONDA_ENV_NAME"
    
    if [[ "$CONDA_DEFAULT_ENV" != "$CONDA_ENV_NAME" ]]; then
        print_error "conda环境激活失败"
        exit 1
    fi
    
    print_success "conda环境激活成功: $CONDA_DEFAULT_ENV"
    
    # 显示Python版本
    python_version=$(python --version 2>&1)
    print_info "Python版本: $python_version"
}

# 函数：创建conda环境
create_conda_env() {
    print_info "创建conda环境: $CONDA_ENV_NAME"
    
    conda create -n "$CONDA_ENV_NAME" python=3.11 -y
    
    if [[ $? -eq 0 ]]; then
        print_success "conda环境创建成功"
        conda activate "$CONDA_ENV_NAME"
        
        # 安装依赖
        print_info "安装项目依赖..."
        if [[ -f "$PROJECT_DIR/requirements.txt" ]]; then
            pip install -r "$PROJECT_DIR/requirements.txt"
        else
            print_warning "未找到requirements.txt文件"
        fi
    else
        print_error "conda环境创建失败"
        exit 1
    fi
}

# 函数：检查项目目录
check_project_directory() {
    print_info "检查项目目录: $PROJECT_DIR"
    
    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "项目目录不存在: $PROJECT_DIR"
        print_info "请确保项目已正确克隆到指定位置"
        exit 1
    fi
    
    # 切换到项目目录
    cd "$PROJECT_DIR" || {
        print_error "无法进入项目目录"
        exit 1
    }
    
    print_success "项目目录检查通过"
    print_info "当前目录: $(pwd)"
}

# 函数：检查API配置
check_api_configuration() {
    print_info "检查API配置..."
    
    if [[ ! -f ".env" ]]; then
        print_warning ".env文件不存在"
        if [[ -f ".env.example" ]]; then
            print_info "发现.env.example文件，是否复制为.env? (y/n)"
            read -p "> " copy_env
            if [[ "$copy_env" =~ ^[Yy]$ ]]; then
                cp .env.example .env
                print_success ".env文件已创建"
                print_warning "请编辑.env文件，配置您的API密钥"
            fi
        fi
    else
        print_success ".env文件存在"
    fi
}

# 函数：显示菜单
show_menu() {
    echo ""
    print_header "TradingAgents-CN WSL启动菜单"
    echo "=================================="
    echo "1) 🖥️  CLI命令行界面"
    echo "2) 🌐 Web网页界面"
    echo "3) 🔍 测试数据源连接"
    echo "4) 📊 快速股票分析示例"
    echo "5) 🧪 测试000791分析日志"
    echo "6) 🔧 检查系统状态"
    echo "7) 📝 查看日志文件"
    echo "8) 🛠️  开发者工具"
    echo "9) ❓ 帮助信息"
    echo "0) 🚪 退出"
    echo ""
}

# 函数：启动CLI界面
start_cli() {
    print_header "启动CLI界面"
    print_info "启动命令行交互界面..."
    python cli/main.py
}

# 函数：启动Web界面
start_web() {
    print_header "启动Web界面"
    print_info "启动Web管理界面..."
    print_info "启动后请在浏览器中访问: http://localhost:$WEB_PORT"
    print_warning "按 Ctrl+C 停止Web服务"
    
    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":$WEB_PORT "; then
        print_warning "端口 $WEB_PORT 已被占用"
        read -p "是否使用其他端口? (输入端口号或按Enter使用默认): " new_port
        if [[ -n "$new_port" ]]; then
            WEB_PORT="$new_port"
        fi
    fi
    
    python -m streamlit run web/app.py --server.port "$WEB_PORT" --server.address localhost
}

# 函数：测试数据源
test_datasources() {
    print_header "测试数据源连接"
    print_info "测试各种数据源的连接状态..."
    python test_datasources.py
}

# 函数：快速分析示例
quick_analysis() {
    print_header "快速股票分析示例"
    print_info "运行中国股票数据分析示例..."
    python test_china_stock_data.py
}

# 函数：测试000791分析日志
test_logging() {
    print_header "测试000791分析日志"
    print_info "运行增强日志系统测试..."
    python test_logging_with_000791.py
}

# 函数：检查系统状态
check_system_status() {
    print_header "系统状态检查"
    
    echo "📋 系统信息："
    echo "  - 操作系统: $(uname -a)"
    echo "  - Python版本: $(python --version)"
    echo "  - Conda环境: $CONDA_DEFAULT_ENV"
    echo "  - 项目目录: $(pwd)"
    
    echo ""
    echo "📦 关键依赖检查："
    
    # 检查关键包
    packages=("streamlit" "pandas" "numpy" "requests" "python-dotenv")
    for package in "${packages[@]}"; do
        if python -c "import $package" 2>/dev/null; then
            version=$(python -c "import $package; print($package.__version__)" 2>/dev/null || echo "未知")
            print_success "$package: $version"
        else
            print_error "$package: 未安装"
        fi
    done
    
    echo ""
    echo "🔧 配置文件检查："
    
    files=(".env" "requirements.txt" "web/app.py" "cli/main.py")
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            print_success "$file: 存在"
        else
            print_error "$file: 不存在"
        fi
    done
}

# 函数：查看日志文件
view_logs() {
    print_header "查看日志文件"
    
    if [[ -d "logs" ]]; then
        echo "📁 可用的日志文件："
        ls -la logs/
        echo ""
        
        read -p "请输入要查看的日志文件名 (或按Enter查看analysis.log): " log_file
        if [[ -z "$log_file" ]]; then
            log_file="analysis.log"
        fi
        
        if [[ -f "logs/$log_file" ]]; then
            print_info "显示日志文件: logs/$log_file"
            echo "=================================="
            tail -20 "logs/$log_file"
            echo "=================================="
            print_info "显示最后20行，完整日志请使用: cat logs/$log_file"
        else
            print_error "日志文件不存在: logs/$log_file"
        fi
    else
        print_warning "日志目录不存在，请先运行一些分析操作"
    fi
}

# 函数：开发者工具
developer_tools() {
    print_header "开发者工具"
    echo "1) 🔍 检查日志系统"
    echo "2) 🧪 运行单元测试"
    echo "3) 📊 分析缓存状态"
    echo "4) 🔧 清理缓存"
    echo "5) 📝 生成系统报告"
    echo ""
    
    read -p "请选择工具 (1-5): " tool_choice
    
    case $tool_choice in
        1)
            print_info "运行日志系统检查..."
            python check_logging_system.py
            ;;
        2)
            print_info "运行单元测试..."
            if [[ -d "tests" ]]; then
                python -m pytest tests/ -v
            else
                print_warning "测试目录不存在"
            fi
            ;;
        3)
            print_info "分析缓存状态..."
            python analyze_execution_logs.py
            ;;
        4)
            print_warning "清理缓存将删除所有缓存数据"
            read -p "确认清理? (y/n): " confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                rm -rf tradingagents/dataflows/data_cache/*
                print_success "缓存已清理"
            fi
            ;;
        5)
            print_info "生成系统报告..."
            {
                echo "# TradingAgents-CN 系统报告"
                echo "生成时间: $(date)"
                echo ""
                echo "## 系统信息"
                uname -a
                echo ""
                echo "## Python环境"
                python --version
                pip list
                echo ""
                echo "## 项目状态"
                ls -la
            } > system_report.txt
            print_success "系统报告已生成: system_report.txt"
            ;;
        *)
            print_error "无效选择"
            ;;
    esac
}

# 函数：显示帮助信息
show_help() {
    print_header "帮助信息"
    echo ""
    echo "📖 TradingAgents-CN WSL启动脚本使用指南"
    echo ""
    echo "🎯 功能说明："
    echo "  - 自动检测和配置WSL环境"
    echo "  - 管理conda虚拟环境"
    echo "  - 提供多种启动方式"
    echo "  - 集成开发和调试工具"
    echo ""
    echo "🔧 环境要求："
    echo "  - Windows WSL (推荐WSL2)"
    echo "  - Conda或Miniconda"
    echo "  - Python 3.10+"
    echo ""
    echo "📚 相关文档："
    echo "  - README-CN.md: 项目概述"
    echo "  - docs/: 详细文档"
    echo "  - 中国证券数据源配置指南.md: 数据源配置"
    echo ""
    echo "🆘 常见问题："
    echo "  1. conda环境激活失败 -> 检查conda安装和PATH配置"
    echo "  2. 端口被占用 -> 使用其他端口或关闭占用进程"
    echo "  3. API密钥错误 -> 检查.env文件配置"
    echo ""
    echo "📞 获取帮助："
    echo "  - GitHub Issues: https://github.com/hsliuping/TradingAgents-CN/issues"
    echo "  - 邮箱: <EMAIL>"
}

# 主函数
main() {
    # 显示欢迎信息
    clear
    print_header "$PROJECT_NAME WSL启动器"
    echo "适用于Windows WSL环境的一键启动脚本"
    echo "=================================================="
    
    # 环境检查
    check_wsl_environment
    init_conda
    check_project_directory
    activate_conda_env
    check_api_configuration
    
    # 主循环
    while true; do
        show_menu
        read -p "请选择操作 (0-9): " choice
        
        case $choice in
            1) start_cli ;;
            2) start_web ;;
            3) test_datasources ;;
            4) quick_analysis ;;
            5) test_logging ;;
            6) check_system_status ;;
            7) view_logs ;;
            8) developer_tools ;;
            9) show_help ;;
            0) 
                print_success "感谢使用 $PROJECT_NAME!"
                exit 0
                ;;
            *)
                print_error "无效选择，请输入0-9之间的数字"
                ;;
        esac
        
        echo ""
        read -p "按Enter键继续..." 
    done
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
