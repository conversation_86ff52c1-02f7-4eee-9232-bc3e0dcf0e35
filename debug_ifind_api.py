#!/usr/bin/env python3
"""
调试iFinD HTTP API连接问题
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def debug_ifind_token_request():
    """调试iFinD token请求"""
    print("🔍 调试iFinD HTTP API Token请求")
    print("=" * 50)
    
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    
    if not refresh_token:
        print("❌ 未找到IFIND_REFRESH_TOKEN环境变量")
        return
    
    print(f"✅ 找到refresh_token，长度: {len(refresh_token)}")
    print(f"Token前50字符: {refresh_token[:50]}...")
    
    # 测试获取access_token的不同方法
    base_url = "https://ft.10jqka.com.cn"
    
    # 方法1: POST请求，refresh_token在header中
    print("\n🧪 方法1: POST请求，refresh_token在header中")
    print("-" * 40)
    
    url1 = f"{base_url}/api/v1/get_access_token"
    headers1 = {
        "Content-Type": "application/json",
        "refresh_token": refresh_token
    }
    
    try:
        print(f"请求URL: {url1}")
        print(f"请求头: {dict(headers1)}")
        
        response1 = requests.post(url1, headers=headers1, timeout=30)
        
        print(f"响应状态码: {response1.status_code}")
        print(f"响应头: {dict(response1.headers)}")
        print(f"响应内容: {response1.text}")
        
        if response1.status_code == 200:
            try:
                result = response1.json()
                print(f"JSON解析成功: {result}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2: POST请求，refresh_token在body中
    print("\n🧪 方法2: POST请求，refresh_token在body中")
    print("-" * 40)
    
    url2 = f"{base_url}/api/v1/get_access_token"
    headers2 = {
        "Content-Type": "application/json"
    }
    body2 = {
        "refresh_token": refresh_token
    }
    
    try:
        print(f"请求URL: {url2}")
        print(f"请求头: {dict(headers2)}")
        print(f"请求体: {body2}")
        
        response2 = requests.post(url2, headers=headers2, json=body2, timeout=30)
        
        print(f"响应状态码: {response2.status_code}")
        print(f"响应头: {dict(response2.headers)}")
        print(f"响应内容: {response2.text}")
        
        if response2.status_code == 200:
            try:
                result = response2.json()
                print(f"JSON解析成功: {result}")
                
                # 如果成功，尝试提取access_token
                if result.get('status_code') == 0:
                    access_token = result.get('data', {}).get('access_token')
                    if access_token:
                        print(f"✅ 成功获取access_token: {access_token[:20]}...")
                        return access_token
                else:
                    print(f"❌ API返回错误: {result.get('reason', '未知错误')}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    # 方法3: GET请求，refresh_token在header中
    print("\n🧪 方法3: GET请求，refresh_token在header中")
    print("-" * 40)
    
    url3 = f"{base_url}/api/v1/get_access_token"
    headers3 = {
        "Content-Type": "application/json",
        "refresh_token": refresh_token
    }
    
    try:
        print(f"请求URL: {url3}")
        print(f"请求头: {dict(headers3)}")
        
        response3 = requests.get(url3, headers=headers3, timeout=30)
        
        print(f"响应状态码: {response3.status_code}")
        print(f"响应头: {dict(response3.headers)}")
        print(f"响应内容: {response3.text}")
        
        if response3.status_code == 200:
            try:
                result = response3.json()
                print(f"JSON解析成功: {result}")
                
                # 如果成功，尝试提取access_token
                if result.get('status_code') == 0:
                    access_token = result.get('data', {}).get('access_token')
                    if access_token:
                        print(f"✅ 成功获取access_token: {access_token[:20]}...")
                        return access_token
                else:
                    print(f"❌ API返回错误: {result.get('reason', '未知错误')}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    return None

def test_with_access_token(access_token):
    """使用access_token测试数据获取"""
    if not access_token:
        print("\n❌ 没有有效的access_token，跳过数据测试")
        return
    
    print(f"\n🧪 使用access_token测试数据获取")
    print("=" * 50)
    
    base_url = "https://ft.10jqka.com.cn"
    
    # 测试实时行情
    print("\n📊 测试实时行情获取")
    print("-" * 30)
    
    url = f"{base_url}/ds_service/api/v1/real_time_quotation"
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    params = {
        "codes": "000001.SZ",
        "indicators": "open,high,low,latest"
    }
    
    try:
        print(f"请求URL: {url}")
        print(f"请求头: {dict(headers)}")
        print(f"请求参数: {params}")
        
        response = requests.post(url, json=params, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"JSON解析成功: {result}")
                
                if result.get('status_code') == 0:
                    print("✅ 实时行情获取成功")
                else:
                    print(f"❌ API返回错误: {result.get('reason', '未知错误')}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"❌ 实时行情测试失败: {e}")

def check_network_connectivity():
    """检查网络连接"""
    print("\n🌐 检查网络连接")
    print("=" * 30)
    
    test_urls = [
        "https://www.baidu.com",
        "https://ft.10jqka.com.cn",
        "https://quantapi.51ifind.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 失败: {e}")

def main():
    """主函数"""
    print("🔍 iFinD HTTP API 调试工具")
    print("=" * 60)
    
    # 检查网络连接
    check_network_connectivity()
    
    # 调试token请求
    access_token = debug_ifind_token_request()
    
    # 如果获取到access_token，测试数据获取
    test_with_access_token(access_token)
    
    print(f"\n{'='*60}")
    print("🏁 调试完成")
    
    if access_token:
        print("✅ iFinD API调试成功，可以正常使用")
    else:
        print("❌ iFinD API调试失败，需要进一步排查")
        print("\n💡 可能的解决方案:")
        print("1. 检查refresh_token是否正确")
        print("2. 确认refresh_token是否已过期")
        print("3. 检查网络连接是否正常")
        print("4. 联系同花顺技术支持")

if __name__ == "__main__":
    main()
