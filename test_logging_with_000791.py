#!/usr/bin/env python3
"""
测试增强日志系统，专门跟踪000791的分析过程
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from enhanced_logging_system import TradingAgentsLogger

def test_000791_analysis_with_logging():
    """测试000791分析过程的日志记录"""
    
    print("🧪 测试000791分析过程的日志记录")
    print("=" * 60)
    
    # 创建日志记录器
    logger = TradingAgentsLogger(log_dir="logs", enable_console=True)
    
    # 模拟分析过程
    stock_code = "000791"
    analysis_date = "2025-07-02"
    
    # 1. 开始分析
    session_id = logger.log_analysis_start(stock_code, analysis_date)
    print(f"📋 开始分析会话: {session_id}")
    
    try:
        # 2. 记录数据访问
        logger.log_data_access(stock_code, "tdx", "获取实时数据", session_id)
        logger.log_data_access(stock_code, "tdx", "获取历史数据", session_id)
        
        # 3. 记录分析步骤
        logger.log_step("数据预处理", stock_code, "清理和格式化数据", session_id)
        logger.log_step("技术指标计算", stock_code, "计算MA、RSI、MACD", session_id)
        logger.log_step("基本面分析", stock_code, "分析财务数据", session_id)
        
        # 4. 模拟缓存操作
        cache_key = f"{stock_code}_stock_data_test123"
        logger.log_cache_operation(stock_code, "保存缓存", cache_key, session_id)
        
        # 5. 实际调用数据获取函数（如果可用）
        try:
            from tradingagents.dataflows.tdx_utils import get_china_stock_data
            
            logger.log_step("调用get_china_stock_data", stock_code, 
                           f"参数: start_date=2025-06-01, end_date={analysis_date}", session_id)
            
            # 实际获取数据
            result = get_china_stock_data(stock_code, "2025-06-01", analysis_date)
            
            # 检查结果中是否有其他股票代码
            if "000001" in result:
                logger.log_error(stock_code, "数据混淆", "结果中包含000001内容", session_id)
                print("⚠️  警告：检测到数据混淆！")
            else:
                logger.log_step("数据验证", stock_code, "数据纯净，无混淆", session_id)
                print("✅ 数据验证通过")
            
            # 分析结果长度
            result_length = len(result)
            logger.log_step("结果分析", stock_code, f"结果长度: {result_length}字符", session_id)
            
        except ImportError:
            logger.log_error(stock_code, "导入错误", "无法导入tdx_utils模块", session_id)
        except Exception as e:
            logger.log_error(stock_code, "数据获取错误", str(e), session_id)
        
        # 6. 结束分析
        logger.log_analysis_end(stock_code, session_id, success=True)
        print("✅ 分析完成")
        
    except Exception as e:
        logger.log_error(stock_code, "分析失败", str(e), session_id)
        logger.log_analysis_end(stock_code, session_id, success=False)
        print(f"❌ 分析失败: {e}")
    
    return session_id

def analyze_log_files():
    """分析生成的日志文件"""
    print("\n📊 分析生成的日志文件")
    print("=" * 50)
    
    log_dir = Path("logs")
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return
    
    log_files = {
        "analysis.log": "分析日志",
        "data_access.log": "数据访问日志", 
        "audit.log": "审计日志",
        "errors.log": "错误日志"
    }
    
    for log_file, description in log_files.items():
        log_path = log_dir / log_file
        
        if log_path.exists():
            print(f"\n📄 {description} ({log_file}):")
            print("-" * 30)
            
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                if lines:
                    print(f"总行数: {len(lines)}")
                    print("最新条目:")
                    
                    # 显示最后几行
                    for line in lines[-3:]:
                        line = line.strip()
                        if line:
                            # 高亮显示000791相关的条目
                            if "000791" in line:
                                print(f"  🎯 {line}")
                            else:
                                print(f"     {line}")
                else:
                    print("文件为空")
                    
            except Exception as e:
                print(f"❌ 读取失败: {e}")
        else:
            print(f"\n❌ {description} 文件不存在")

def check_for_confusion_in_logs():
    """检查日志中是否有混淆迹象"""
    print("\n🔍 检查日志中的混淆迹象")
    print("=" * 50)
    
    log_dir = Path("logs")
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return
    
    confusion_found = False
    
    for log_file in log_dir.glob("*.log"):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否在000791的分析过程中出现000001
            lines = content.split('\n')
            
            in_791_session = False
            current_session = None
            
            for i, line in enumerate(lines):
                # 检测会话开始
                if "000791" in line and "ANALYSIS_START" in line:
                    in_791_session = True
                    # 提取会话ID
                    parts = line.split('|')
                    if len(parts) >= 3:
                        current_session = parts[2].strip()
                    print(f"📋 发现000791分析会话: {current_session}")
                
                # 检测会话结束
                elif "000791" in line and "ANALYSIS_END" in line:
                    in_791_session = False
                    current_session = None
                
                # 在000791会话中检查是否出现000001
                elif in_791_session and "000001" in line:
                    confusion_found = True
                    print(f"⚠️  在000791会话中发现000001: 第{i+1}行")
                    print(f"    {line.strip()}")
        
        except Exception as e:
            print(f"❌ 检查文件 {log_file} 失败: {e}")
    
    if not confusion_found:
        print("✅ 未在日志中发现混淆迹象")
    
    return confusion_found

def generate_analysis_report():
    """生成分析报告"""
    print("\n📋 日志分析报告")
    print("=" * 50)
    
    print("✅ 增强日志系统功能:")
    print("1. ✅ 分析过程的完整跟踪")
    print("2. ✅ 数据访问的详细记录")
    print("3. ✅ 错误和异常的捕获")
    print("4. ✅ 会话ID的唯一标识")
    print("5. ✅ 多层级的日志分类")
    
    print("\n💡 使用建议:")
    print("1. 在每次分析000791时启用此日志系统")
    print("2. 定期检查 logs/ 目录中的日志文件")
    print("3. 使用会话ID跟踪完整的分析流程")
    print("4. 关注 errors.log 中的异常信息")
    print("5. 通过 audit.log 进行审计跟踪")
    
    print("\n🔧 集成方法:")
    print("1. 将 TradingAgentsLogger 集成到主分析流程中")
    print("2. 在 CLI 工具中添加日志记录调用")
    print("3. 为关键函数添加日志装饰器")
    print("4. 设置定期的日志分析任务")

if __name__ == "__main__":
    print("🧪 TradingAgents-CN 000791分析日志测试")
    print("=" * 60)
    
    # 1. 测试000791分析过程
    session_id = test_000791_analysis_with_logging()
    
    # 2. 分析日志文件
    analyze_log_files()
    
    # 3. 检查混淆迹象
    confusion_found = check_for_confusion_in_logs()
    
    # 4. 生成报告
    generate_analysis_report()
    
    print(f"\n{'='*60}")
    print(f"🏁 测试完成 | 会话ID: {session_id}")
    
    if confusion_found:
        print("⚠️  发现潜在的混淆问题，请查看日志详情")
    else:
        print("✅ 未发现混淆问题")
