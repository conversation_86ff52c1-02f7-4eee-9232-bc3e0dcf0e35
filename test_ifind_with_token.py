#!/usr/bin/env python3
"""
使用access_token直接测试iFinD数据获取
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_ifind_realtime_data():
    """测试iFinD实时数据"""
    print("📊 测试iFinD实时数据获取")
    print("=" * 40)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    if not access_token:
        print("❌ 未找到IFIND_ACCESS_TOKEN")
        return False
    
    print(f"✅ 使用access_token: {access_token[:20]}...")
    
    # 测试实时行情API
    url = "https://ft.10jqka.com.cn/ds_service/api/v1/real_time_quotation"
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    # 测试多个股票
    test_stocks = [
        ("000001.SZ", "平安银行"),
        ("600036.SH", "招商银行"),
        ("000858.SZ", "五粮液")
    ]
    
    for stock_code, stock_name in test_stocks:
        print(f"\n📈 测试股票: {stock_code} ({stock_name})")
        
        params = {
            "codes": stock_code,
            "indicators": "open,high,low,latest,preClose,volume,amount,turnoverRate,pe,pb"
        }
        
        try:
            response = requests.post(url, json=params, headers=headers, timeout=30)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"API状态码: {result.get('status_code')}")
                
                if result.get('status_code') == 0:
                    print("✅ 数据获取成功")
                    
                    # 解析数据
                    if 'data' in result and 'tables' in result['data']:
                        tables = result['data']['tables']
                        if tables and 'table' in tables[0] and tables[0]['table']:
                            data = tables[0]['table'][0]
                            
                            print(f"  最新价: {data.get('latest', 'N/A')}")
                            print(f"  开盘价: {data.get('open', 'N/A')}")
                            print(f"  最高价: {data.get('high', 'N/A')}")
                            print(f"  最低价: {data.get('low', 'N/A')}")
                            print(f"  昨收价: {data.get('preClose', 'N/A')}")
                            print(f"  成交量: {data.get('volume', 'N/A')}")
                            print(f"  成交额: {data.get('amount', 'N/A')}")
                            print(f"  换手率: {data.get('turnoverRate', 'N/A')}%")
                            print(f"  市盈率: {data.get('pe', 'N/A')}")
                            print(f"  市净率: {data.get('pb', 'N/A')}")
                        else:
                            print("⚠️ 数据格式异常")
                    else:
                        print("⚠️ 响应中无数据")
                else:
                    error_msg = result.get('reason', '未知错误')
                    print(f"❌ API返回错误: {error_msg}")
            else:
                print(f"❌ HTTP请求失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return True

def test_ifind_historical_data():
    """测试iFinD历史数据"""
    print("\n📈 测试iFinD历史数据获取")
    print("=" * 40)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    if not access_token:
        print("❌ 未找到IFIND_ACCESS_TOKEN")
        return False
    
    # 测试历史数据API
    url = "https://ft.10jqka.com.cn/ds_service/api/v1/historical_quotation"
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token
    }
    
    # 测试参数
    stock_code = "000001.SZ"
    params = {
        "codes": stock_code,
        "indicators": "open,high,low,close,volume,amount",
        "start_date": "20250601",
        "end_date": "20250702",
        "period": "day"
    }
    
    print(f"📊 测试股票: {stock_code}")
    print(f"📅 时间范围: 2025-06-01 到 2025-07-02")
    
    try:
        response = requests.post(url, json=params, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API状态码: {result.get('status_code')}")
            
            if result.get('status_code') == 0:
                print("✅ 历史数据获取成功")
                
                # 解析数据
                if 'data' in result and 'tables' in result['data']:
                    tables = result['data']['tables']
                    if tables and 'table' in tables[0] and tables[0]['table']:
                        data_list = tables[0]['table']
                        
                        print(f"  数据条数: {len(data_list)}")
                        
                        if data_list:
                            # 显示最近几条数据
                            print("  最近5条数据:")
                            for i, data in enumerate(data_list[-5:], 1):
                                date = data.get('time', 'N/A')
                                open_price = data.get('open', 'N/A')
                                high_price = data.get('high', 'N/A')
                                low_price = data.get('low', 'N/A')
                                close_price = data.get('close', 'N/A')
                                volume = data.get('volume', 'N/A')
                                
                                print(f"    {i}. {date}: 开{open_price} 高{high_price} 低{low_price} 收{close_price} 量{volume}")
                    else:
                        print("⚠️ 数据格式异常")
                else:
                    print("⚠️ 响应中无数据")
            else:
                error_msg = result.get('reason', '未知错误')
                print(f"❌ API返回错误: {error_msg}")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return True

def test_ifind_quota():
    """测试iFinD配额使用情况"""
    print("\n📊 测试iFinD配额信息")
    print("=" * 40)
    
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    
    if not access_token:
        print("❌ 未找到IFIND_ACCESS_TOKEN")
        return False
    
    # 根据您提供的配额信息
    quotas = {
        "行情数据": {"used": 0, "total": 150000000},
        "基础数据": {"used": 0, "total": 5000000},
        "宏观指标": {"used": 0, "total": 5000000},
        "特色数据": {"used": 0, "total": 20000},
        "公告函数": {"used": 0, "total": 1000000},
        "公告下载": {"used": 0, "total": 100}
    }
    
    print("📈 您的iFinD配额状况:")
    for quota_type, info in quotas.items():
        used = info["used"]
        total = info["total"]
        percentage = (used / total) * 100 if total > 0 else 0
        status = "充裕" if percentage < 50 else "紧张" if percentage < 80 else "告急"
        
        print(f"  {quota_type}: {used:,}/{total:,} ({percentage:.1f}%) - {status}")
    
    print("\n💡 配额说明:")
    print("  - 行情数据: 1.5亿次调用，适合大量实时数据获取")
    print("  - 基础数据: 500万次调用，用于基本面数据")
    print("  - 宏观指标: 500万次调用，用于宏观经济数据")
    print("  - 特色数据: 2万次调用，用于特殊指标")
    print("  - 公告相关: 用于公司公告和财报数据")
    
    return True

def main():
    """主测试函数"""
    print("🧪 iFinD HTTP API 直接测试")
    print("=" * 60)
    print("使用您提供的access_token进行测试")
    
    # 检查配置
    access_token = os.getenv('IFIND_ACCESS_TOKEN')
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    
    print(f"\n🔑 Token配置状态:")
    print(f"  refresh_token: {'✅ 已配置' if refresh_token else '❌ 未配置'}")
    print(f"  access_token: {'✅ 已配置' if access_token else '❌ 未配置'}")
    
    if not access_token:
        print("\n❌ 缺少access_token，无法进行测试")
        print("💡 请确保在.env文件中配置了IFIND_ACCESS_TOKEN")
        return
    
    # 执行测试
    tests = [
        ("配额信息测试", test_ifind_quota),
        ("实时数据测试", test_ifind_realtime_data),
        ("历史数据测试", test_ifind_historical_data),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print(f"\n{'='*60}")
    print("🏁 测试结果汇总")
    print("-" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} | {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！iFinD数据源可以正常使用")
        print("\n💡 下一步:")
        print("1. 可以在TradingAgents-CN中使用iFinD数据源")
        print("2. 在Web界面或CLI中选择iFinD作为数据源")
        print("3. access_token有效期7天，到期后会自动从refresh_token获取新的")
    else:
        print("⚠️ 部分测试失败，请检查网络连接和Token配置")

if __name__ == "__main__":
    main()
