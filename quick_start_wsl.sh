#!/bin/bash
# TradingAgents-CN WSL快速启动脚本
# 简化版本，适用于已配置好环境的用户

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🚀 TradingAgents-CN WSL快速启动${NC}"
echo "=================================="

# 初始化conda
echo -e "${BLUE}📦 初始化conda环境...${NC}"
source ~/miniconda3/etc/profile.d/conda.sh 2>/dev/null || source ~/anaconda3/etc/profile.d/conda.sh 2>/dev/null

# 激活TACN环境
echo -e "${BLUE}🔄 激活conda环境 TACN...${NC}"
conda activate /home/<USER>/miniconda/envs/TACN 2>/dev/null || conda activate TACN 2>/dev/null

# 进入项目目录
cd /mnt/e/AI/TradingAgents-CN

# 显示当前状态
echo -e "${GREEN}✅ 环境准备完成${NC}"
echo -e "${BLUE}📍 当前目录: $(pwd)${NC}"
echo -e "${BLUE}🐍 Python版本: $(python --version)${NC}"
echo -e "${BLUE}🌍 Conda环境: $CONDA_DEFAULT_ENV${NC}"

echo ""
echo -e "${YELLOW}🎯 快速启动选项:${NC}"
echo "1) Web界面 (推荐)"
echo "2) CLI命令行"
echo "3) 测试数据源"
echo "4) 分析000791"
echo ""

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo -e "${GREEN}🌐 启动Web界面...${NC}"
        echo -e "${BLUE}💡 浏览器访问: http://localhost:8501${NC}"
        python -m streamlit run web/app.py --server.port 8501 --server.address localhost
        ;;
    2)
        echo -e "${GREEN}🖥️ 启动CLI界面...${NC}"
        python cli/main.py
        ;;
    3)
        echo -e "${GREEN}🔍 测试数据源...${NC}"
        python test_datasources.py
        ;;
    4)
        echo -e "${GREEN}📊 分析000791...${NC}"
        python test_logging_with_000791.py
        ;;
    *)
        echo -e "${YELLOW}⚠️ 无效选择，启动Web界面${NC}"
        python -m streamlit run web/app.py --server.port 8501 --server.address localhost
        ;;
esac
